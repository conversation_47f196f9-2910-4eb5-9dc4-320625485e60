package com.mercaso.ims.infrastructure.excel.mapper;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.infrastructure.excel.data.ItemAdjustmentDetailData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ItemAdjustmentDetailDataMapper {

    ItemAdjustmentDetailDataMapper INSTANCE = Mappers.getMapper(ItemAdjustmentDetailDataMapper.class);

    @Mapping(target = "itemDescription", source = "title")
    @Mapping(target = "packSize", source = "packageSize")
    @Mapping(target = "itemSize", source = "itemSize")
    ItemAdjustmentDetailData toExcelData(ItemAdjustmentRequestDetail domain);

}
