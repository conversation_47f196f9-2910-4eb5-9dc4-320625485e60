package com.mercaso.ims.infrastructure.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mercaso.ims.infrastructure.exception.SerializationException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SerializationUtils {

    private static final ObjectMapper objectMapper = standardObjectMapper();

    public static ObjectMapper standardObjectMapper() {
        return new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public static <S> S deserialize(String json, Class<S> domainClass) {
        S domain = null;

        try {
            domain = objectMapper.readValue(json, domainClass);
        } catch (IOException e) {
            throw new SerializationException("N/A", e);
        }

        return domain;
    }

    public static Map<String, Object> entityToMap(Object entity) {
        return objectMapper.convertValue(entity, new TypeReference<HashMap<String, Object>>() {
        });
    }

    public static Map<String, Object> deserializeToMap(String json) {
        HashMap<String, Object> hashMap;

        try {
            hashMap = objectMapper.readValue(json, new TypeReference<HashMap<String, Object>>() {
            });
        } catch (IOException e) {
            throw new SerializationException("N/A", e);
        }

        return hashMap;
    }

    public static String serialize(Object object) {
        String json;
        try {
            json = objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            throw new SerializationException("N/A", e);
        }

        return json;
    }

    public static JsonNode toTree(Object object) {
        return objectMapper.valueToTree(object);
    }

    public static JsonNode readTree(String json) {
        try {
            return objectMapper.readTree(json);
        } catch (JsonProcessingException e) {
            throw new SerializationException("readTree exception", e);
        }
    }


    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) {
        try {
            return objectMapper.readValue(content, valueTypeRef);
        } catch (Exception e) {
            throw new SerializationException("readValue exception", e);
        }
    }

    public static <T> T treeToValue(TreeNode n, Class<T> valueType) {
        try {
            return objectMapper.treeToValue(n, valueType);
        } catch (Exception e) {
            throw new SerializationException("treeToValue exception", e);
        }
    }
}
