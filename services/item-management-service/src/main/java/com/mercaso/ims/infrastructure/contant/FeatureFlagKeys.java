package com.mercaso.ims.infrastructure.contant;

public class FeatureFlagKeys {

    public static final String IMS_EXCEPTION_RECORD = "ims_exception_record";

    public static final String IMS_SYNC_DIFY_KNOWLEDGE = "ims_sync_dify_knowledge";

    public static final String IMS_ADD_MISSING_UPC_REASON = "ims_add_missing_upc_reason";

    public static final String IMS_ADD_RATE_LIMITS_FOR_FINALE_API = "ims_add_rate_limits_for_finale_api";

    public static final String IMS_ARCHIVED_REASON = "ims_archived_reason";

    public static final String IMS_NEGATIVE_MARGIN_VALIDATION = "ims_negative_margin_validation";

    public static final String SUPPLIER_REBATES = "supplier_rebates";

    private FeatureFlagKeys() {
    }


}
