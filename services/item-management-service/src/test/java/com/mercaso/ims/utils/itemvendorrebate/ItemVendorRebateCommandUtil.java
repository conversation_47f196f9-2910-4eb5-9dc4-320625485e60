package com.mercaso.ims.utils.itemvendorrebate;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

public class ItemVendorRebateCommandUtil {

    public static CreateItemVendorRebateCommand buildCreateItemVendorRebateCommand(
            UUID vendorItemId, UUID vendorId, UUID itemId) {
        return CreateItemVendorRebateCommand.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(Instant.now())
                .endDate(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC)
                        .plusMonths(6)
                        .toInstant(ZoneOffset.UTC))
                .rebatePerUnit(new BigDecimal("5.00"))
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    public static CreateItemVendorRebateCommand buildCreateItemVendorRebateCommand(
            UUID vendorItemId, UUID vendorId, UUID itemId, Instant startDate, Instant endDate, BigDecimal rebatePerUnit) {
        return CreateItemVendorRebateCommand.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(rebatePerUnit)
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    public static UpdateItemVendorRebateCommand buildUpdateItemVendorRebateCommand(
            UUID id, UUID vendorItemId, UUID vendorId, UUID itemId) {
        return UpdateItemVendorRebateCommand.builder()
                .id(id)
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(Instant.now())
                .endDate(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC)
                        .plusMonths(12)
                        .toInstant(ZoneOffset.UTC))
                .rebatePerUnit(new BigDecimal("7.50"))
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    public static UpdateItemVendorRebateCommand buildUpdateItemVendorRebateCommand(
            UUID id, UUID vendorItemId, UUID vendorId, UUID itemId,
            Instant startDate, Instant endDate, BigDecimal rebatePerUnit) {
        return UpdateItemVendorRebateCommand.builder()
                .id(id)
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(rebatePerUnit)
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }
}
