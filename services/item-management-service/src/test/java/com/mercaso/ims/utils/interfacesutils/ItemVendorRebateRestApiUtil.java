package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ItemVendorRebateRestApiUtil extends IntegrationTestRestUtil {

    private static final String ITEM_VENDOR_REBATE_REST_API_URL = "/v1/item-vendor-rebates";

    public ItemVendorRebateRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemVendorRebateDto createItemVendorRebateRequest(CreateItemVendorRebateCommand command) throws Exception {
        return createEntity(ITEM_VENDOR_REBATE_REST_API_URL, command, ItemVendorRebateDto.class);
    }

    public ItemVendorRebateDto updateItemVendorRebateRequest(UpdateItemVendorRebateCommand command) throws Exception {
        return updateEntity(ITEM_VENDOR_REBATE_REST_API_URL, command, ItemVendorRebateDto.class);
    }

    public void deleteItemVendorRebateRequest(UUID id) {
        deleteEntity(ITEM_VENDOR_REBATE_REST_API_URL + "/" + id);
    }
}
