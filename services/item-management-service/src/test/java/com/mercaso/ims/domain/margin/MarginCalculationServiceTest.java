package com.mercaso.ims.domain.margin;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MarginCalculationServiceTest {

    @Mock
    private ItemVendorRebateService itemVendorRebateService;

    private MarginCalculationService marginCalculationService;

    @BeforeEach
    void setUp() {
        marginCalculationService = new MarginCalculationService(itemVendorRebateService);
    }

    @Test
    void testCalculateMargin_WithoutRebate() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = new BigDecimal("6.00");
        int precision = 2;

        // When
        BigDecimal result = marginCalculationService.calculateMargin(price, cost, precision);

        // Then
        // Expected: (10.00 - 6.00) / 10.00 = 0.40
        assertEquals(new BigDecimal("0.40"), result);
    }

    @Test
    void testCalculateMarginWithRebate_BasicCalculation() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = new BigDecimal("6.00");
        BigDecimal rebate = new BigDecimal("1.00");
        int precision = 2;

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithRebate(price, cost, rebate, precision);

        // Then
        // Expected: (10.00 - (6.00 - 1.00)) / 10.00 = (10.00 - 5.00) / 10.00 = 0.50
        assertEquals(new BigDecimal("0.50"), result);
    }

    @Test
    void testCalculateMarginWithRebate_NullRebate() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = new BigDecimal("6.00");
        BigDecimal rebate = null;
        int precision = 2;

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithRebate(price, cost, rebate, precision);

        // Then
        // Expected: (10.00 - 6.00) / 10.00 = 0.40 (rebate treated as 0)
        assertEquals(new BigDecimal("0.40"), result);
    }

    @Test
    void testCalculateMarginWithRebate_NullPrice() {
        // Given
        BigDecimal price = null;
        BigDecimal cost = new BigDecimal("6.00");
        BigDecimal rebate = new BigDecimal("1.00");
        int precision = 2;

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithRebate(price, cost, rebate, precision);

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testCalculateMarginWithRebate_NullCost() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = null;
        BigDecimal rebate = new BigDecimal("1.00");
        int precision = 2;

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithRebate(price, cost, rebate, precision);

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testCalculateMarginWithRebate_ZeroPrice() {
        // Given
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal cost = new BigDecimal("6.00");
        BigDecimal rebate = new BigDecimal("1.00");
        int precision = 2;

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithRebate(price, cost, rebate, precision);

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testCalculateMarginWithAutoRebate_WithApplicableRebate() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = new BigDecimal("6.00");
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant effectiveDate = Instant.now();
        int precision = 2;

        ItemVendorRebate rebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(effectiveDate.minus(Duration.ofDays(1)))
                .endDate(null) // continuous rebate
                .rebatePerUnit(new BigDecimal("1.00"))
                .build();

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenReturn(Collections.singletonList(rebate));

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithAutoRebate(
                price, cost, vendorId, itemId, effectiveDate, precision);

        // Then
        // Expected: (10.00 - (6.00 - 1.00)) / 10.00 = 0.50
        assertEquals(new BigDecimal("0.50"), result);
    }

    @Test
    void testCalculateMarginWithAutoRebate_NoRebateFound() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = new BigDecimal("6.00");
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant effectiveDate = Instant.now();
        int precision = 2;

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenReturn(Collections.emptyList());

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithAutoRebate(
                price, cost, vendorId, itemId, effectiveDate, precision);

        // Then
        // Expected: (10.00 - 6.00) / 10.00 = 0.40 (no rebate)
        assertEquals(new BigDecimal("0.40"), result);
    }

    @Test
    void testGetApplicableRebate_WithMultipleRebates() {
        // Given
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant effectiveDate = Instant.now();

        ItemVendorRebate oldRebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .startDate(effectiveDate.minus(Duration.ofDays(10)))
                .endDate(effectiveDate.minus(Duration.ofDays(1)))
                .rebatePerUnit(new BigDecimal("0.50"))
                .build();

        ItemVendorRebate currentRebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .startDate(effectiveDate.minus(Duration.ofDays(5)))
                .endDate(null) // continuous
                .rebatePerUnit(new BigDecimal("1.00"))
                .build();

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenReturn(Arrays.asList(oldRebate, currentRebate));

        // When
        BigDecimal result = marginCalculationService.getApplicableRebate(vendorId, itemId, effectiveDate);

        // Then
        assertEquals(new BigDecimal("1.00"), result); // Should return the most recent applicable rebate
    }

    @Test
    void testGetApplicableRebate_RebateNotYetEffective() {
        // Given
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant effectiveDate = Instant.now();

        ItemVendorRebate futureRebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .startDate(effectiveDate.plus(Duration.ofDays(1)))
                .endDate(null)
                .rebatePerUnit(new BigDecimal("1.00"))
                .build();

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenReturn(Collections.singletonList(futureRebate));

        // When
        BigDecimal result = marginCalculationService.getApplicableRebate(vendorId, itemId, effectiveDate);

        // Then
        assertEquals(BigDecimal.ZERO, result); // Rebate not yet effective
    }

    @Test
    void testGetApplicableRebate_RebateExpired() {
        // Given
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant effectiveDate = Instant.now();

        ItemVendorRebate expiredRebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .startDate(effectiveDate.minus(Duration.ofDays(10)))
                .endDate(effectiveDate.minus(Duration.ofDays(1)))
                .rebatePerUnit(new BigDecimal("1.00"))
                .build();

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenReturn(Collections.singletonList(expiredRebate));

        // When
        BigDecimal result = marginCalculationService.getApplicableRebate(vendorId, itemId, effectiveDate);

        // Then
        assertEquals(BigDecimal.ZERO, result); // Rebate expired
    }

    @Test
    void testGetApplicableRebate_NullParameters() {
        // When & Then
        assertEquals(BigDecimal.ZERO, marginCalculationService.getApplicableRebate(null, UUID.randomUUID(), Instant.now()));
        assertEquals(BigDecimal.ZERO, marginCalculationService.getApplicableRebate(UUID.randomUUID(), null, Instant.now()));
        assertEquals(BigDecimal.ZERO, marginCalculationService.getApplicableRebate(UUID.randomUUID(), UUID.randomUUID(), null));
    }

    @Test
    void testGetApplicableRebate_ServiceException() {
        // Given
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant effectiveDate = Instant.now();

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenThrow(new RuntimeException("Database error"));

        // When
        BigDecimal result = marginCalculationService.getApplicableRebate(vendorId, itemId, effectiveDate);

        // Then
        assertEquals(BigDecimal.ZERO, result); // Should handle exceptions gracefully
    }

    @Test
    void testCalculateMarginWithAutoRebate_CurrentDate() {
        // Given
        BigDecimal price = new BigDecimal("10.00");
        BigDecimal cost = new BigDecimal("6.00");
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        int precision = 4;

        ItemVendorRebate rebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(Instant.now().minus(Duration.ofDays(1)))
                .endDate(null)
                .rebatePerUnit(new BigDecimal("1.50"))
                .build();

        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId))
                .thenReturn(Collections.singletonList(rebate));

        // When
        BigDecimal result = marginCalculationService.calculateMarginWithAutoRebate(
                price, cost, vendorId, itemId, precision);

        // Then
        // Expected: (10.00 - (6.00 - 1.50)) / 10.00 = (10.00 - 4.50) / 10.00 = 0.5500
        assertEquals(new BigDecimal("0.5500"), result);
    }
}
