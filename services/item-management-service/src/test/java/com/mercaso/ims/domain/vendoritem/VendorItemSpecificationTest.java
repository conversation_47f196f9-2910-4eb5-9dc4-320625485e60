package com.mercaso.ims.domain.vendoritem;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceRepository;
import com.mercaso.ims.domain.margin.MarginCalculationService;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class VendorItemSpecificationTest {

    @Mock
    ItemRepository itemRepository;
    @Mock
    ItemRegPriceRepository itemRegPriceRepository;
    @Mock
    MarginCalculationService marginCalculationService;
    @InjectMocks
    VendorItemSpecification vendorItemSpecification;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testIsReasonableMargin() {
        Item item = ItemUtil.buildItem();
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(item.getId());
        when(itemRepository.findById(any())).thenReturn(item);
        when(itemRegPriceRepository.findByItemId(any(UUID.class))).thenReturn(itemRegPrice);
        when(itemRegPriceRepository.findById(any())).thenReturn(itemRegPrice);
        
        // Mock margin calculation to return margins with small difference (within 20% threshold)
        when(marginCalculationService.calculateMarginWithAutoRebate(any(), any(), any(), any(), anyInt()))
            .thenReturn(new BigDecimal("0.5000")) // Original margin: 50%
            .thenReturn(new BigDecimal("0.6000")); // New margin: 60% (10 percentage point difference, within threshold)

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setPackPlusCrvCost(BigDecimal.TEN);
        boolean result = vendorItemSpecification.isReasonableMargin(vendorItem, new BigDecimal("10"));
        Assertions.assertEquals(true, result);
    }

    @Test
    void testIsReasonableMarginAsNewCostIsNull() {
        Item item = ItemUtil.buildItem();
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(item.getId());
        when(itemRepository.findById(any())).thenReturn(item);
        when(itemRegPriceRepository.findByItemId(any(UUID.class))).thenReturn(itemRegPrice);
        when(itemRegPriceRepository.findById(any())).thenReturn(itemRegPrice);

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setPackPlusCrvCost(BigDecimal.TEN);
        boolean result = vendorItemSpecification.isReasonableMargin(vendorItem, null);
        Assertions.assertFalse(result);
    }
}