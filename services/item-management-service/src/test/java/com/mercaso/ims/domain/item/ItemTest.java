package com.mercaso.ims.domain.item;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ItemType;
import com.mercaso.ims.domain.item.enums.PackageType;
import com.mercaso.ims.domain.item.enums.SalesStatus;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.itemattribute.ItemAttributeUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
class ItemTest {


    @Test
    void testUpdateItemTagsByBrandName() {

        Item item = ItemUtil.buildItem("Sku Number", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
        String brandName = "New Brand Name";
        // Act
        item.setItemDefaultTags(brandName);

        // Assert
        List<ItemTag> itemTags = item.getItemTags();
        assertEquals(6, itemTags.size());
        assertEquals(item.getDepartment(), itemTags.get(0).getTagName());
        assertEquals(item.getCategory(), itemTags.get(1).getTagName());
        assertEquals(item.getSubCategory(), itemTags.get(2).getTagName());
        assertEquals(item.getClazz(), itemTags.get(3).getTagName());
        assertEquals("Brand_" + brandName, itemTags.get(4).getTagName());
        assertEquals("UPP:" + item.getPackageSize(), itemTags.get(5).getTagName());
    }

    @Test
    void testSetItemTags() {

        Item item = ItemUtil.buildItem("Sku Number", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
        String brandName = "New Brand Name";
        // Act
        item.setItemDefaultTags(brandName);

        // Assert
        List<ItemTag> itemTags = item.getItemTags();
        assertEquals(6, itemTags.size());

    }

    @Test
    void testUpdateItemTags() {
        String tag1 = RandomStringUtils.randomAlphabetic(5);
        String tag2 = RandomStringUtils.randomAlphabetic(5);
        String tag3 = RandomStringUtils.randomAlphabetic(5);

        Item item = ItemUtil.buildItem("Sku Number", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
        String brandName = "New Brand Name";
        item.setItemDefaultTags(brandName);

        // Act
        item.updateItemTags(Arrays.asList(tag1, tag2, tag3));

        List<String> tags = Arrays.asList(tag1, tag2, tag3);
        // Assert
        List<ItemTag> itemTags = item.getItemTags();
        assertEquals(3, itemTags.size());

        assertTrue(
            itemTags.stream().allMatch(itemTag -> tags.contains(itemTag.getTagName()))
        );

    }

    @Test
    void testUpdate() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        Item item = ItemUtil.buildItem("Sku Number", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
        UUID id = UUID.randomUUID();
        UUID categoryId = UUID.randomUUID();
        String brandId = UUID.randomUUID().toString();
        UUID primaryVendorId = UUID.randomUUID();

        // Act
        Item actualUpdateResult = item.update(new UpdateItemCommand(id, categoryId, brandId, "Name", "Dr", "42",
            "The characteristics of someone or something", "Note", "<EMAIL>",
            "<EMAIL>", primaryVendorId, null, "Detail",
            PackageType.UNKNOWN, 3, "Shelf Life", ItemType.SELF_OPERATED, SalesStatus.LISTING, AvailabilityStatus.ACTIVE,
            1L, 1L, "Handle", UUID.randomUUID(), "New Description",
            true, "Department", "Category", "Sub Category",
            "Clazz", null, null, null,
            null,
            null, null, null, null, null, null, null, null, null, null));

        // Assert
        assertEquals("42", item.getSkuNumber());
        assertEquals("Category", item.getCategory());
        assertEquals("Clazz", item.getClazz());
        assertEquals("Department", item.getDepartment());
        assertEquals("Detail", item.getDetail());
        assertEquals("Dr", item.getTitle());
        assertEquals("Note", item.getNote());
        assertEquals("Shelf Life", item.getShelfLife());
        assertEquals("Sub Category", item.getSubCategory());
        assertEquals("The characteristics of someone or something", item.getDescription());
        assertEquals(1L, item.getCompanyId().longValue());
        assertEquals(1L, item.getLocationId().longValue());
        assertEquals(3, item.getPackageSize().intValue());
        assertEquals(AvailabilityStatus.ACTIVE, item.getAvailabilityStatus());
        assertEquals(ItemType.SELF_OPERATED, item.getItemType());
        assertEquals(PackageType.UNKNOWN, item.getPackageType());
        assertEquals(SalesStatus.LISTING, item.getSalesStatus());
        assertSame(item, actualUpdateResult);
        assertEquals(UUID.fromString(brandId), item.getBrandId());
        assertSame(categoryId, item.getCategoryId());
        assertSame(primaryVendorId, item.getPrimaryVendorId());
    }

    @Test
    void updateItemAttributesIfItemAttributesIsEmpty() {

        // Arrange
        Item item = ItemUtil.buildItem("Sku Number", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());

        // Act
        item.updateItemAttributes(new ArrayList<>());

        // Assert
        assertEquals(0, item.getItemAttributes().size());

    }

    @Test
    void testUpdateItemAttributes() {
        List<ItemAttribute> attributes = new ArrayList<>();
        attributes.add(ItemAttributeUtil.buildItemAttribute());
        attributes.add(ItemAttributeUtil.buildItemAttribute());
        // Arrange
        Item item = ItemUtil.buildItem("Sku Number", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());

        // Act
        item.updateItemAttributes(attributes);

        // Assert
        assertEquals(2, item.getItemAttributes().size());

    }
}
