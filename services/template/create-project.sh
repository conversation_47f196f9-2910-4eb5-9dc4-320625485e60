#!/bin/bash

# 读取配置文件
source project-config.properties

# 获取当前目录路径
current_dir=$(pwd)

# services 目录路径
services_dir=$(dirname "$current_dir")

# 获取父目录
parent_dir=$(dirname "$services_dir")

# 新项目的目录路径
new_project_dir="$services_dir/$projectName"

# 创建新项目目录
mkdir -p "$new_project_dir"

# 使用 rsync 复制文件，并排除 .exclude 文件中列出的文件和目录
rsync -av --exclude-from="$current_dir/.exclude" "$current_dir/" "$new_project_dir"

# 进入新项目目录
cd "$new_project_dir"

# 修改项目名称和包名
sed -i "" "s/template/$projectName/g" settings.gradle
sed -i "" "s/com.mercaso/$groupId/g" build.gradle

# 重命名Java包目录
mv src/main/java/com/mercaso/template src/main/java/com/mercaso/$packageName
mv src/test/java/com/mercaso/template src/test/java/com/mercaso/$packageName

# 修改Java文件中的包名
find src/main/java -type f -name "*.java" -exec sed -i "" "s/com.mercaso.template/com.mercaso.$packageName/g" {} +
find src/test/java -type f -name "*.java" -exec sed -i "" "s/com.mercaso.template/com.mercaso.$packageName/g" {} +

# 修改app.yaml文件中的app和name字段
sed -i '' "s/app: template/app: $projectName/g" $projectName/k8s/app.yaml
sed -i '' "s/name: template/name: $projectName/g" $projectName/k8s/app.yaml

# 修改父目录中的 settings.gradle 文件，添加 include 'services:new-project'
echo -e "\ninclude 'services:$projectName'" >> "$parent_dir/settings.gradle"
echo "findProject(':services:$projectName')?.name = '$projectName'" >> "$parent_dir/settings.gradle"

echo "Project $projectName created successfully in $new_project_dir."
