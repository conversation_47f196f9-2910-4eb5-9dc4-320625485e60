plugins {
    id "org.sonarqube"
    id "jacoco"
}

sonar {
    properties {
        property "sonar.projectKey", "premier-store-os_${project.name}"
        property "sonar.organization", "mercaso"
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml"
        def branchName = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
        property "sonar.branch.name", branchName
    }
}

jacocoTestReport {
    dependsOn test, integrationTest
    reports {
        xml.required = true
    }
}

test {
    finalizedBy jacocoTestReport
}

dependencies {
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
}

springBoot {
    mainClass = 'com.mercaso.template.Application'
}
