# Template

这是一个 Gradle 项目模板，旨在帮助团队快速创建和配置新的 Java 项目。该模板遵循领域驱动设计（DDD）的原则，并提供了基本的项目结构和配置。

## 项目结构

```plaintext
my-gradle-template/
├── .exclude
├── build.gradle
├── create-project.sh
├── project-config.properties
├── settings.gradle
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/
│   │           └── mercaso/
│   └── test/
│       └── java/
│           └── com/
│               └── mercaso/
└── README.md
```

## 使用说明

### 1. 克隆模板项目

首先，克隆主项目到本地：

```sh
git clone https://github.com/atlas-mono/Premier-Store-OS.git
cd Premier-Store-OS/services/template
```

### 2. 编辑配置文件

在项目template根目录下有一个 `project-config.properties` 文件。编辑该文件以配置新项目的名称。

```properties
projectName=newProjectName
```

### 3. 运行创建脚本

在编辑好配置文件后，运行 `create-project.sh` 脚本。该脚本将会在 `services` 目录下创建一个新的项目目录，并自动修改相关配置。

```sh
chmod +x create-project.sh
./create-project.sh
```

### 4. 确认新项目设置

脚本将会完成以下任务：

1. 创建新项目目录 `services/newProjectName`。
2. 复制模板项目中的文件到新项目目录，并排除 `.exclude` 文件中列出的文件。
3. 修改 `settings.gradle` 和 `build.gradle` 文件中的项目名称和包名。
4. 重命名 Java 包目录。
5. 更新父目录中的 `settings.gradle` 文件，添加 `include 'services:newProjectName'` 和 `findProject(':services:newProjectName')?.name = 'newProjectName'` 语句。

## 自定义文件排除

如果需要排除某些文件或目录，可以编辑 `.exclude` 文件。该文件中列出需要排除的文件或目录。

```plaintext
# Ignore script itself
create-project.sh

# Ignore .exclude
.exclude

# Ignore project-specific configuration file
project-config.properties

# Ignore Git-related files and directories
.git/
.gitignore

# Ignore build
build/

# Ignore README
README.md
```

## 项目维护

如果在使用中遇到任何问题或有任何建议，请随时提交 Issue 或联系@Billy。

```