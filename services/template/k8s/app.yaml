apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: template
  name: template
  namespace: default
spec:
  replicas: $REPLICAS
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      app: template
  template:
    metadata:
      labels:
        app: template
    spec:
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: ${APPLICATION_NAME}
      containers:
        - name: template
          image: ${DOCKER_REPO_URL}/template:${IMAGE_VERSION}
          imagePullPolicy: Always
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: $SPRING_PROFILES_ACTIVE
            - name: JAEGER_AGENT_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          # Graceful shutdown hack - sleep 15 seconds to give the k8s network
          # config time to update and stop routing new requests to this pod.
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - sleep 15
          ports:
            - containerPort: 8080
              protocol: TCP
              name: http
            - containerPort: 8081
              protocol: TCP
              name: monitoring
            - containerPort: 1099
              protocol: TCP
              name: jmx
          resources:
            requests:
              memory: $REQUESTS_MEMORY
              cpu: $REQUESTS_CPU
            limits:
              memory: $LIMITS_MEMORY
              cpu: $LIMITS_CPU
          livenessProbe:
            httpGet:
              path: actuator/health/liveness
              port: monitoring
            initialDelaySeconds: 120
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: actuator/health/readiness
              port: monitoring
            successThreshold: 2
            failureThreshold: 2
            periodSeconds: 5
            timeoutSeconds: 5
          startupProbe:
            httpGet:
              path: /actuator/health/readiness
              port: monitoring
            successThreshold: 1
            periodSeconds: 5
            failureThreshold: 60
      terminationGracePeriodSeconds: 180
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: template
    #archetype: next-spring-boot-v1
  name: template
  namespace: default
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
    - name: monitoring
      port: 8081
      protocol: TCP
  selector:
    app: template
  type: ClusterIP
