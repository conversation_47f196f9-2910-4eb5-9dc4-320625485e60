FROM eclipse-temurin:21-jdk-alpine

ARG APPLICATION_NAME
ARG APP_PORT

ENV APPLICATION ${APPLICATION_NAME}

ENV JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -XX:MaxRAMPercentage=50 -Djava.security.egd=file:/dev/./urandom"
ENV JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false"
ENV JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.local.only=false -Dcom.sun.management.jmxremote.port=1099 -Dcom.sun.management.jmxremote.rmi.port=1099"
ENV JAVA_OPTS="${JAVA_OPTS} -Djava.rmi.server.hostname=127.0.0.1"
ENV JAVA_OPTS="${JAVA_OPTS} -DEnableLogstash"

RUN apk update \
    && apk upgrade \
    && apk add --no-cache bash \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p /var/run/mercaso \
    && chmod -R 755 /var/run/mercaso \
    && mkdir -p /opt/mercaso/${APPLICATION_NAME} \
    && chmod -R 755 /opt/mercaso/${APPLICATION_NAME} \
    && mkdir -p /var/log/mercaso/${APPLICATION_NAME} \
    && chmod -R 755 /var/log/mercaso/${APPLICATION_NAME} \
    && mkdir -p /usr/bin \
    && chmod -R 755 /usr/bin \
    && ln -s /var/log/mercaso/${APPLICATION_NAME} /opt/mercaso/${APPLICATION_NAME}/log \
    && echo "ALL : ALL " >> /etc/hosts.allow \
    && printf "#!/bin/sh\nexec java ${JAVA_OPTS} -jar template.jar \"$@\"" > /opt/mercaso/${APPLICATION_NAME}/run.sh \
    && chmod 755 /opt/mercaso/${APPLICATION_NAME}/run.sh

WORKDIR /opt/mercaso/${APPLICATION_NAME}

EXPOSE ${APP_PORT}

COPY build/libs/*-1.0.0.jar ./template.jar

ENTRYPOINT ["./run.sh"]
