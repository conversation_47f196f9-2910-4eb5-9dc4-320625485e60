package com.mercaso.data.master_catalog.enums;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public enum PotentiallyDuplicateRawDataStatus {
    /**
     * The potentially duplicate master catalog raw data is not yet reviewed
     */
    PENDING_REVIEW,
    /**
     * The potentially duplicate master catalog raw data is marked as a duplicate or not a duplicate
     */
    IN_STAGE,
    /**
     * The potentially duplicate master catalog raw data is first round reviewed
     */
    FIRST_ROUND_REVIEWED,
    /**
     * The potentially duplicate master catalog raw data is reviewed
     */
    SECOND_ROUND_REVIEWED
}
