package com.mercaso.data.master_catalog.entity;

import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_potentially_duplicate_raw_data")
public class MasterCatalogPotentiallyDuplicateRawData extends BaseEntity {

    /**
     * Unique identifier of the master catalog raw data
     */
    @Column(name = "raw_data_id")
    private UUID rawDataId;

    /**
     * UPC of the master catalog raw data
     */
    @Size(max = 64)
    @Column(name = "upc", length = 64)
    private String upc;

    /**
     * Description of the master catalog raw data
     */
    @Column(name = "name", length = 255)
    private String name;

    /**
     * Primary vendor of the master catalog raw data
     */
    @Size(max = 256)
    @Column(name = "primary_vendor", length = 256)
    private String primaryVendor;

    /**
     * Unique identifier of the potentially duplicate master catalog raw data
     */
    @Column(name = "potentially_duplicate_raw_data_id")
    private UUID potentiallyDuplicateRawDataId;

    /**
     * UPC of the potentially duplicate master catalog raw data
     */
    @Size(max = 64)
    @Column(name = "potentially_duplicate_upc", length = 64)
    private String potentiallyDuplicateUpc;

    /**
     * Description of the potentially duplicate master catalog raw data
     */
//    @NotNull
    @Column(name = "potentially_duplicate_name", length = 255)
    private String potentiallyDuplicateName;

    /**
     * Vendor of the potentially duplicate master catalog raw data
     */
    @Size(max = 256)
    @Column(name = "potentially_duplicate_vendor", length = 256)
    private String potentiallyDuplicateVendor;

    @NotNull
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private PotentiallyDuplicateRawDataStatus status;

    @NotNull
    @Column(name = "job_id", nullable = false)
    private UUID jobId;

    @Column(name = "task_id")
    private UUID taskId;

    @Column(name = "duplicated")
    private Boolean duplicated;

    /**
     * User who created the task
     */
    @Size(max = 64)
    @NotNull
    @Column(name = "created_by", nullable = false, length = 64)
    private String createdBy;

    /**
     * User who last updated the task
     */
    @Size(max = 64)
    @NotNull
    @Column(name = "updated_by", nullable = false, length = 64)
    private String updatedBy;
}
