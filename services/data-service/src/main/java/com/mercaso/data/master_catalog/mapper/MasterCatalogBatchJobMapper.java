package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogBatchJobMapper extends
    BaseDoMapper<MasterCatalogBatchJobDto, MasterCatalogBatchJob> {

    MasterCatalogBatchJobListDto toListDto(MasterCatalogBatchJob entity);
}