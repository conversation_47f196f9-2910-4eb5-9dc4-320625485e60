package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataUpdatedPayload;

@EventPublishConfig(
    publishLocal = true
)
public class MasterCatalogRawDataUpdatedEvent extends BaseApplicationEvent<MasterCatalogRawDataUpdatedPayload> {

    public MasterCatalogRawDataUpdatedEvent(Object source, MasterCatalogRawDataUpdatedPayload payload) {
        super(source, payload);
    }
}
