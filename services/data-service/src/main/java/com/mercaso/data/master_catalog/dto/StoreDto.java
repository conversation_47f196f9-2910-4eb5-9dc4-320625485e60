package com.mercaso.data.master_catalog.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.Store}
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreDto extends BaseDto {

    @NotNull
    private UUID id;

    @NotNull
    @Size(max = 255)
    private String name;

    private boolean restockEnabled;
}
