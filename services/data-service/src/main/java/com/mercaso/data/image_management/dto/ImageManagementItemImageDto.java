package com.mercaso.data.image_management.dto;

import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ImageManagementItemImageDto {

    String id;
    String imageId;
    String imageType;
    String imageAngel;
    String sku;
    String upc;
    Boolean isPrimary;
    Instant createdAt;
    Instant updatedAt;
    Boolean isEach;
    String imageUrl;
}
