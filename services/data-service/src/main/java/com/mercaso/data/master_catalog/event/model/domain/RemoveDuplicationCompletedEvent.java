package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;

@EventPublishConfig(
    publishLocal = true
)
public class RemoveDuplicationCompletedEvent extends BaseApplicationEvent<RemoveDuplicationCompletedPayload> {

    public RemoveDuplicationCompletedEvent(Object source, RemoveDuplicationCompletedPayload payload) {
        super(source, payload);
    }
} 