package com.mercaso.data.master_catalog.adaptor.impl;

import static io.pinecone.commons.IndexInterface.buildUpsertVectorWithUnsignedIndices;

import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.mercaso.data.master_catalog.adaptor.VectorDBAdapter;
import com.mercaso.data.master_catalog.config.PineconeConfig;
import io.pinecone.clients.Index;
import io.pinecone.clients.Inference;
import io.pinecone.clients.Pinecone;
import io.pinecone.clients.Pinecone.Builder;
import io.pinecone.exceptions.PineconeConfigurationException;
import io.pinecone.exceptions.PineconeException;
import io.pinecone.exceptions.PineconeNotFoundException;
import io.pinecone.exceptions.PineconeValidationException;
import io.pinecone.proto.UpsertResponse;
import io.pinecone.unsigned_indices_model.QueryResponseWithUnsignedIndices;
import io.pinecone.unsigned_indices_model.ScoredVectorWithUnsignedIndices;
import io.pinecone.unsigned_indices_model.VectorWithUnsignedIndices;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.db_control.client.model.DeletionProtection;
import org.openapitools.db_control.client.model.IndexModel;
import org.openapitools.inference.client.ApiException;
import org.openapitools.inference.client.model.Embedding;
import org.openapitools.inference.client.model.EmbeddingsList;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PineconeApiAdapterImpl implements VectorDBAdapter {

    private final PineconeConfig pineconeConfig;
    private Index index;
    private Pinecone pinecone;
    @Override
    public List<List<UUID>> getDuplicationPredictionForInBatch(List<String> idList,
        List<String> nameList, List<Boolean> packTypeList, List<Struct> metaDataList,
        String indexName,
        Float threshold, int batchSize) {

        try {
            pinecone = getPineconeInstance(pineconeConfig.getApiKey());
            index = pinecone.getIndexConnection(indexName);
        } catch (PineconeNotFoundException e) {
            log.error("Index not found: {}", indexName);
            return Collections.emptyList();
        } catch (PineconeConfigurationException e) {
            log.error("Pinecone key error for index {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Pinecone connect to index error {} ", e.toString());
            return Collections.emptyList();
        }

        try {
            safelyDeleteIndex(indexName, pinecone);
            waitForIndexDeletion(indexName, pinecone, 60000);
        } catch (PineconeException e) {
            log.error("Delete index error : {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (TimeoutException e) {
            return Collections.emptyList();
        }

        try {
            safelyCreateIndex(indexName, pinecone);
            waitForIndexCreation(indexName, pinecone, 60000);
        } catch (PineconeException e) {
            log.error("Create index error : {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (TimeoutException e) {
            return Collections.emptyList();
        }

        List<Embedding> embeddings;
        List<List<Embedding>> embeddingsList = new ArrayList<>();
        try {
            //Slice the task in batch for upserting into Pinecone
            int count = 0;
            List<List<String>> textBatch = chunk(nameList, batchSize);
            for (List<String> chunk : textBatch) {
                embeddingsList.add(getEmbeddings(pinecone, chunk));
                count += chunk.size();
                log.info("Get embedding complete. Embedding total count:{}, now count: {}",
                    nameList.size(), count);
            }
        } catch (ApiException e) {
            log.error("Pinecone getEmbedding Error, {}", e.toString());
            return Collections.emptyList();
        }
        embeddings = embeddingsList.stream().flatMap(List::stream).toList();

        List<VectorWithUnsignedIndices> vectors = createVectorsForUpsert(
            embeddings,
            idList,
            metaDataList);
        index.close();
        try {
            index = pinecone.getIndexConnection(indexName);
        } catch (PineconeNotFoundException e) {
            log.error("Index not found: {}", indexName);
            return Collections.emptyList();
        } catch (PineconeConfigurationException e) {
            log.error("Pinecone key error for index {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Pinecone connect to index error {} ", e.toString());
            return Collections.emptyList();
        }

        UpsertResponse upsertResponse;
        List<List<VectorWithUnsignedIndices>> vectorsChunk = chunk(vectors, batchSize);
        try {
            int count = 0;
            for (List<VectorWithUnsignedIndices> vector : vectorsChunk) {
                upsertResponse = index.upsert(vector, "");
                count += upsertResponse.getUpsertedCount();
                log.info("Upsert complete. Upsert total count:{}, now count: {}",
                    vectors.size(), count);
            }
            waitForVectorsUpsert(index, vectors.size(), 600000);
        } catch (PineconeValidationException e) {
            log.error("Invalid argument for upserting index: {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error when upserting index, {}", e.toString());
            return Collections.emptyList();
        }
        //Give Pinecone sometime to setup.
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Collections.emptyList();
        }
        List<Struct> metaList = createFiltersForPackTypeQuery(packTypeList);
        List<List<Struct>> metaDataListChunk = chunk(metaList, batchSize);

        List<QueryResponseWithUnsignedIndices> queryResponseWithUnsignedIndices;
        List<List<QueryResponseWithUnsignedIndices>> queryResponseWithUnsignedIndicesList = new ArrayList<>();

        try {
            int count = 0;
            for (int i = 0; i < embeddingsList.size(); i++) {
                queryResponseWithUnsignedIndicesList.add(listQuery(
                    index,
                    embeddingsList.get(i), metaDataListChunk.get(i), 2, false, false));
                count += embeddingsList.get(i).size();
                log.info("Query complete. Query total count:{}, now count: {}",
                    embeddings.size(), count);
            }

        } catch (PineconeValidationException e) {
            log.error("Invalid argument for querying index: {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error when querying index, {}", e.toString());
            return Collections.emptyList();
        }
        queryResponseWithUnsignedIndices = queryResponseWithUnsignedIndicesList.stream()
            .flatMap(List::stream).toList();
        List<List<UUID>> finalResult = filterOutPineconeResultToGetUniquePotentialDuplicationInBatch(
            idList, queryResponseWithUnsignedIndices, threshold);
        log.info("In batch duplication found {} data", finalResult.size());
        return finalResult;
    }

    @Override
    public List<List<UUID>> getDuplicationPredictionForProduct(List<String> idList,
        List<String> nameList, List<Boolean> packTypeList, String indexName,
        Float threshold, int batchSize) {
        try {
            pinecone = getPineconeInstance(pineconeConfig.getApiKey());
            index = pinecone.getIndexConnection(indexName);
        } catch (PineconeNotFoundException e) {
            log.error("Index not found: {}", indexName);
            return Collections.emptyList();
        } catch (PineconeConfigurationException e) {
            log.error("Pinecone key error for index {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Pinecone connect to index error {} ", e.toString());
            return Collections.emptyList();
        }

        List<Embedding> embeddings;
        List<List<Embedding>> embeddingsList = new ArrayList<>();
        try {
            //Slice the task in batch for Pinecone
            int count = 0;
            List<List<String>> textBatch = chunk(nameList, batchSize);
            for (List<String> chunk : textBatch) {
                embeddingsList.add(getEmbeddings(pinecone, chunk));
                count += chunk.size();
                log.info("Get embedding complete. Embedding total count:{}, now count: {}",
                    nameList.size(), count);
            }
        } catch (ApiException e) {
            log.error("Pinecone getEmbedding Error, {}", e.toString());
            return Collections.emptyList();
        }
        embeddings = embeddingsList.stream().flatMap(List::stream).toList();
        List<QueryResponseWithUnsignedIndices> queryResponseWithUnsignedIndices;
        List<List<QueryResponseWithUnsignedIndices>> queryResponseWithUnsignedIndicesList = new ArrayList<>();
        List<List<Boolean>> packTypeBatch = chunk(packTypeList, batchSize);

        try {
            int count = 0;
            for (int i = 0; i < embeddingsList.size(); i++) {
                queryResponseWithUnsignedIndicesList.add(listQuery(
                    index,
                    embeddingsList.get(i),
                    createFiltersForPackTypeQuery(packTypeBatch.get(i)),
                    1, false, false)
                );
                count += embeddingsList.get(i).size();
                log.info("Query complete. Query total count:{}, now count: {}",
                    embeddings.size(), count);
            }
        } catch (PineconeValidationException e) {
            log.error("Invalid argument for querying index: {}, {}", indexName, e.toString());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error when querying index, {}", e.toString());
            return Collections.emptyList();
        }
        queryResponseWithUnsignedIndices = queryResponseWithUnsignedIndicesList.stream()
            .flatMap(List::stream).toList();
        List<List<UUID>> finalResult = filterOutPineconeResultToGetUniquePotentialDuplicationWithProduct(
            idList, queryResponseWithUnsignedIndices, threshold);
        log.info("Product duplication found {} data", finalResult.size());
        return finalResult;
    }

    @Override
    public Integer upsertToProductTable(
        List<String> idList, List<String> nameList, List<Struct> metaDataList,
        String indexName, int batchSize) {
        try {
            pinecone = getPineconeInstance(pineconeConfig.getApiKey());
            index = pinecone.getIndexConnection(indexName);
        } catch (PineconeNotFoundException e) {
            log.error("Index not found: {}", indexName);
            return 0;
        } catch (PineconeConfigurationException e) {
            log.error("Pinecone key error for index {}, {}", indexName, e.toString());
            return 0;
        } catch (Exception e) {
            log.error("Pinecone connect to index error, {}", e.toString());
            return 0;
        }

        List<Embedding> embeddings;
        List<List<Embedding>> embeddingsList = new ArrayList<>();
        try {
            //Slice the task in batch for Pinecone
            int count = 0;
            List<List<String>> textBatch = chunk(nameList, batchSize);
            for (List<String> chunk : textBatch) {
                embeddingsList.add(getEmbeddings(pinecone, chunk));
                count += chunk.size();
                log.info("Get embedding complete. Embedding total count:{}, now count: {}",
                    nameList.size(), count);
            }
        } catch (ApiException e) {
            log.error("Pinecone getEmbedding Error, {}", e.toString());
            return 0;
        }
        embeddings = embeddingsList.stream().flatMap(List::stream).toList();
        List<VectorWithUnsignedIndices> vectors = createVectorsForUpsert(embeddings, idList,
            metaDataList);

        UpsertResponse upsertResponse;
        List<List<VectorWithUnsignedIndices>> vectorsChunk = chunk(vectors, batchSize);
        int upsertCount = 0;
        try {
            for (List<VectorWithUnsignedIndices> vector : vectorsChunk) {
                upsertResponse = index.upsert(vector, "");
                upsertCount += upsertResponse.getUpsertedCount();
                log.info("Upsert complete. Upsert total count:{}, now count: {}",
                    vectors.size(), upsertCount);
            }
            waitForVectorsUpsert(index, vectors.size(), 600000);
        } catch (PineconeValidationException e) {
            log.error("Invalid argument for upserting index: {}, {}", indexName, e.toString());
            return 0;
        } catch (Exception e) {
            log.error("Error when upserting index, {}", e.toString());
            return 0;
        }
        log.info("Upsert {} records successfully to index {}", upsertCount, indexName);
        return upsertCount;
    }

    private List<List<UUID>> filterOutPineconeResultToGetUniquePotentialDuplicationInBatch(
        List<String> idList,
        List<QueryResponseWithUnsignedIndices> queryResponseWithUnsignedIndices, Float threshold) {
        Set<Set<UUID>> uniquePairs = new HashSet<>();
        List<List<UUID>> finalResult = new ArrayList<>();
        for (int i = 0; i < queryResponseWithUnsignedIndices.size(); i++) {
            QueryResponseWithUnsignedIndices response = queryResponseWithUnsignedIndices.get(i);
            UUID potentialRawDataId = UUID.fromString(idList.get(i));
            Set<UUID> pair = new TreeSet<>();

            // Check if there are any matches
            if (!response.getMatchesList().isEmpty() && response.getMatchesList().size() == 2) {
                ScoredVectorWithUnsignedIndices duplicate = response.getMatches(1);
                if (duplicate.getScore() > threshold) {
                    // Add the duplicate if it passes the threshold
                    pair.add(UUID.fromString(duplicate.getId()));
                }
            }
            // Always add the original potential raw data ID
            pair.add(potentialRawDataId);

            // Only add unique pairs to the result
            if (uniquePairs.add(pair)) {
                finalResult.add(new ArrayList<>(pair));
            }
        }
        return finalResult;
    }

    private List<List<UUID>> filterOutPineconeResultToGetUniquePotentialDuplicationWithProduct(
        List<String> idList,
        List<QueryResponseWithUnsignedIndices> queryResponseWithUnsignedIndices, Float threshold) {
        List<List<UUID>> finalResult = new ArrayList<>();

        for (int i = 0; i < queryResponseWithUnsignedIndices.size(); i++) {
            UUID potentialRawDataId = UUID.fromString(idList.get(i));
            if (queryResponseWithUnsignedIndices.get(i).getMatchesList().isEmpty()) {
                finalResult.add(Collections.singletonList(potentialRawDataId));
                continue;
            }
            ScoredVectorWithUnsignedIndices duplicatedInstance = queryResponseWithUnsignedIndices.get(
                i).getMatches(0);
            UUID rawDataIdInProduct = UUID.fromString(duplicatedInstance.getId());
            if (duplicatedInstance.getScore() > threshold) {
                finalResult.add(
                    new ArrayList<>(Arrays.asList(rawDataIdInProduct, potentialRawDataId)));
            } else {
                finalResult.add(Collections.singletonList(potentialRawDataId));
            }
        }
        return finalResult;
    }


    public List<QueryResponseWithUnsignedIndices> listQuery(Index index,
        List<Embedding> embeddings, List<Struct> filters,
        Integer topK, Boolean includeValues, Boolean includeMetadata) {
        List<QueryResponseWithUnsignedIndices> resultList = new ArrayList<>();
        for (int i = 0; i < embeddings.size(); i++) {
            List<Float> queryVector = embeddings.get(i).getDenseEmbedding().getValues();
            QueryResponseWithUnsignedIndices queryResponse = index.query(topK, queryVector, null,
                null,
                null, null, filters.get(i), includeValues, includeMetadata);
            resultList.add(queryResponse);
        }
        return resultList;
    }

    public QueryResponseWithUnsignedIndices singleQuery(Pinecone pc, String indexName,
        Embedding embedding,
        Integer topK, Boolean includeValues, Boolean includeMeta) {
        Index index = pc.getIndexConnection(indexName);
        List<Float> queryVector = embedding.getDenseEmbedding().getValues();
        return index.query(topK, queryVector, null,
            null, null, null, null,
            includeValues, includeMeta);
    }


    public List<Embedding> getEmbeddings(Pinecone pinecone, List<String> inputs)
        throws ApiException {
        Inference inference = pinecone.getInferenceClient();

        // Specify the embedding model and parameters
        String embeddingModel = "multilingual-e5-large";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("input_type", "query");
        parameters.put("truncate", "END");
        // Generate embeddings for the input data

        EmbeddingsList embeddings;
        embeddings = inference.embed(embeddingModel, parameters, inputs);

        // Get embedded data
        return embeddings.getData();
    }

    private void deleteIndex(String indexName, Pinecone pc) {
        pc.deleteIndex(indexName);
    }

    private IndexModel createIndex(String indexName, Pinecone pc) {
        String similarityMetric = "cosine";
        int dimension = 1024;
        String cloud = "aws";
        String region = "us-west-2";
        HashMap<String, String> tags = new HashMap<>();
        return pc.createServerlessIndex(indexName, similarityMetric, dimension, cloud, region,
            DeletionProtection.DISABLED, tags);

    }

    private static List<Float> convertBigDecimalToFloat(List<BigDecimal> bigDecimalValues) {
        return bigDecimalValues.stream()
            .map(BigDecimal::floatValue)
            .collect(Collectors.toList());
    }

    private List<Struct> createFiltersForPackTypeQuery(List<Boolean> packTypeList) {
        List<Struct> filters = new ArrayList<>();
        for (Boolean aBoolean : packTypeList) {
            filters.add(Struct.newBuilder()
                .putFields("single", Value.newBuilder()
                    .setStructValue(Struct.newBuilder()
                        .putFields("$eq", Value.newBuilder()
                            .setBoolValue(aBoolean)
                            .build()))
                    .build())
                .build());
        }
        return filters;

    }

    public void safelyDeleteIndex(String indexName, Pinecone pinecone) {
        deleteIndex(indexName, pinecone);
    }

    public void waitForIndexDeletion(String indexName, Pinecone pinecone, long timeoutMillis)
        throws TimeoutException {
        long endTime = System.currentTimeMillis() + timeoutMillis;
        while (System.currentTimeMillis() < endTime) {
            try {
                log.info("Waiting for index {} deletion.", indexName);
                pinecone.describeIndex(indexName);
                Thread.sleep(1000);
            } catch (PineconeException e) {
                return;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.error("Timeout reached while waiting for index '{}' deletion.", indexName);
        throw new TimeoutException(
            "Index deletion timed out after " + timeoutMillis + " milliseconds.");
    }

    public void safelyCreateIndex(String indexName, Pinecone pinecone) {
        createIndex(indexName, pinecone);
    }

    public void waitForIndexCreation(String indexName, Pinecone pinecone, long timeoutMillis)
        throws TimeoutException {
        long endTime = System.currentTimeMillis() + timeoutMillis;
        while (System.currentTimeMillis() < endTime) {
            try {
                Thread.sleep(1000);
                log.info("Waiting for index {} creation.", indexName);
                pinecone.describeIndex(indexName);
                return;
            } catch (PineconeException ignore) {
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.error("Timeout reached while waiting for index '{}' creation.", indexName);
        throw new TimeoutException(
            "Index creation timed out after " + timeoutMillis + " milliseconds.");
    }

    public void waitForVectorsUpsert(Index index, int expectedCount, long timeoutMillis)
        throws TimeoutException {
        long endTime = System.currentTimeMillis() + timeoutMillis;
        while (System.currentTimeMillis() < endTime) {
            int currentCount = index.describeIndexStats().getNamespacesMap().get("")
                .getVectorCount();
            if (currentCount >= expectedCount) {
                return;
            }
            log.info("Waiting for vectors upsert: expected {}, current {}", expectedCount,
                currentCount);
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.error("Timeout reached while waiting for upserting on index: {}.", index.toString());
        throw new TimeoutException(
            "Upserting timed out after " + timeoutMillis + " milliseconds.");
    }
    // A helper function that breaks an ArrayList into chunks of batchSize
    private static <T> List<List<T>> chunk(List<T> list, int batchSize) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(list.size(), i + batchSize);
            chunks.add(new ArrayList<>(list.subList(i, end)));
        }
        return chunks;
    }

    public Pinecone getPineconeInstance(String apiKey) {
        return new Builder(apiKey).build();
    }

    private List<VectorWithUnsignedIndices> createVectorsForUpsert(List<Embedding> embeddings,
        List<String> idList, List<Struct> metaDataList) {
        List<VectorWithUnsignedIndices> vectors = new ArrayList<>();
        for (int i = 0; i < embeddings.size(); i++) {
            vectors.add(buildUpsertVectorWithUnsignedIndices(idList.get(i),
                embeddings.get(i).getDenseEmbedding().getValues(), null, null,
                metaDataList.get(i)));
        }
        return vectors;
    }

}
