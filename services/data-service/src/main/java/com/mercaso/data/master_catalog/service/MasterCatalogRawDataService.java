package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogV2Request;
import com.mercaso.data.master_catalog.dto.UpdateMasterCatalogRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import java.util.Collection;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

public interface MasterCatalogRawDataService {

    Page<MasterCatalogRawDataDto> searchMasterCatalogRawData(SearchMasterCatalogRequest searchRequest);

    Page<MasterCatalogRawDataDto> searchMasterCatalogRawDataByImage(MultipartFile file, Pageable pageable);

    void markAsCompleted(Collection<UUID> ids);

  MasterCatalogRawDataDto findById(UUID id);

    Page<MasterCatalogRawDataDto> searchDuplicationRawData(Integer pageNumber,Integer pageSize,String upc);

  Page<MasterCatalogRawDataDto> searchMasterCatalogRawDataV2(SearchMasterCatalogV2Request searchRequest);

  void updateMasterCatalogRawData(UUID id, UpdateMasterCatalogRequest updateRequest);

  void syncMasterCatalogRawData(MasterCatalogRawData sourceData);
}
