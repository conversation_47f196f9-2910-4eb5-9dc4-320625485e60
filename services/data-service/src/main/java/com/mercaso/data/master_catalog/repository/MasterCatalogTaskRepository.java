package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogTaskRepository extends JpaRepository<MasterCatalogTask, UUID>, JpaSpecificationExecutor<MasterCatalogTask> {

  List<MasterCatalogTask> findByJobId(UUID jobId);
  List<MasterCatalogTask> findByJobIdIn(List<UUID> jobIds);
}
