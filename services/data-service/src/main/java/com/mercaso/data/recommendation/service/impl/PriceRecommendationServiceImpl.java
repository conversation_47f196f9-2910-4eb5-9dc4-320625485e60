package com.mercaso.data.recommendation.service.impl;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.dto.SearchPriceDto;
import com.mercaso.data.recommendation.entity.PriceRecommendation;
import com.mercaso.data.recommendation.mapper.PriceRecommendationMapper;
import com.mercaso.data.recommendation.repository.PriceRecommendationRepository;
import com.mercaso.data.recommendation.service.PriceRecommendationService;
import jakarta.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PriceRecommendationServiceImpl implements PriceRecommendationService {

  private static final String DEFAULT_STORE_ID = "b350491e-e238-4caf-b328-cf2438e057d8";
  private final PriceRecommendationRepository priceRecommendationRepository;
  private final PriceRecommendationMapper priceRecommendationMapper;

  @Override
  public PageableResponse<PriceRecommendationDto> search(SearchPriceDto search, Integer pageNumber,
      Integer pageSize) {

    Sort sort = Sort.by(Sort.Direction.DESC, "id");

    if (Boolean.TRUE.equals(search.getLatestOrder())) {
      sort = Sort.by(Sort.Direction.DESC, "newItemFlag", "id");
    }

    Pageable pageable = PageRequest.of(pageNumber, pageSize, sort);

    var recommendations = priceRecommendationRepository.search(search, pageable);

    return PageableResponse.<PriceRecommendationDto>builder()
        .data(recommendations.getContent().stream().map(priceRecommendationMapper::toDto).toList())
        .pageNumber(recommendations.getNumber())
        .pageSize(recommendations.getSize())
        .totalPages(recommendations.getTotalPages())
        .totalElements((int) recommendations.getTotalElements())
        .build();
  }

  @Override
  public PageableResponse<PriceRecommendationDto> searchAll(String storeId, String searchText,
      Pageable pageable) {
    Specification<PriceRecommendation> specification = ((root, query, builder) -> {
      Predicate predicate = builder.equal(root.get("storeId"), DEFAULT_STORE_ID);
      if (StringUtils.isNotBlank(searchText)) {
        String keyword = "%" + searchText.toLowerCase() + "%";
        predicate = builder.and(predicate, builder.like(builder.lower(root.get("name")), keyword));
      }
      return predicate;
    });
    Page<PriceRecommendation> recommendations = priceRecommendationRepository.findAll(specification, pageable);
    List<PriceRecommendationDto> priceRecommendationList = recommendations.getContent().stream().map(priceRecommendationMapper::toDto)
        .peek(priceRecommendationDto -> priceRecommendationDto.setCost(priceRecommendationDto.getLastPurchasePrice()))
        .toList();

    if (StringUtils.isNotBlank(storeId)) {
      List<String> upcList = priceRecommendationList.stream().map(PriceRecommendationDto::getUpc).toList();
      List<PriceRecommendation> storeSpecificData = priceRecommendationRepository.findByStoreIdAndUpcIn(storeId, upcList);

      Map<String, BigDecimal> upcPriceMap = storeSpecificData.stream()
          .collect(Collectors.toMap(
              PriceRecommendation::getUpc,
              p -> new BigDecimal(p.getLastPurchasePrice()),
              (existing, replacement) -> existing
          ));

      priceRecommendationList.forEach(dto -> {
        BigDecimal price = upcPriceMap.get(dto.getUpc());
        if (price != null) {
          dto.setLastPurchasePrice(price);
        }
      });
    }
    return PageableResponse.<PriceRecommendationDto>builder()
        .data(priceRecommendationList)
        .pageNumber(recommendations.getNumber())
        .pageSize(recommendations.getSize())
        .totalPages(recommendations.getTotalPages())
        .totalElements((int) recommendations.getTotalElements())
        .build();
  }

  @Override
  public List<DepartmentDto> findDepartmentsByStoreId(String storeId) {
    var recommendations = priceRecommendationRepository.findByStoreId(storeId);
    return recommendations.stream()
        .map(priceRecommendation ->
            new DepartmentDto(priceRecommendation.getDepartmentId(),
                priceRecommendation.getDepartment()))
        .distinct()
        .toList();
  }

  @Override
  public List<DepartmentDto> getEverGreenDepartments() {
    List<PriceRecommendation> recommendations = priceRecommendationRepository.findByStoreIdAndEverGreen(DEFAULT_STORE_ID, true);
    return recommendations.stream()
        .map(priceRecommendation ->
            new DepartmentDto(priceRecommendation.getDepartmentId(),
                priceRecommendation.getDepartment()))
        .distinct()
        .toList();
  }

  @Override
  public PageableResponse<PriceRecommendationDto> searchEvergreenRecommendations(String departmentId,
      PageRequest pageRequest) {
    Specification<PriceRecommendation> specification = (root, query, criteriaBuilder) -> {
      Predicate everGreen = criteriaBuilder.isTrue(root.get("everGreen"));
      Predicate storeId = criteriaBuilder.equal(root.get("storeId"), DEFAULT_STORE_ID);
      if (departmentId != null && !departmentId.isEmpty()) {
        return criteriaBuilder.and(everGreen, storeId, criteriaBuilder.equal(root.get("departmentId"), departmentId));
      }
      return criteriaBuilder.and(everGreen, storeId);
    };
    Page<PriceRecommendation> recommendations = priceRecommendationRepository.findAll(specification, pageRequest);
    return PageableResponse.<PriceRecommendationDto>builder()
        .data(recommendations.getContent().stream().map(priceRecommendationMapper::toDto).peek(priceRecommendationDto -> priceRecommendationDto.setCost(priceRecommendationDto.getLastPurchasePrice())).toList())
        .pageNumber(recommendations.getNumber())
        .pageSize(recommendations.getSize())
        .totalPages(recommendations.getTotalPages())
        .totalElements((int) recommendations.getTotalElements())
        .build();
  }
}
