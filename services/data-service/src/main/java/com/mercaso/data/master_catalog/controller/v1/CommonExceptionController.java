package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Locale;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class CommonExceptionController {

  private final MessageSource messageSource;

  @ExceptionHandler(value = MasterCatalogBusinessException.class)
  public ResponseEntity<Map<String, Object>> handleMasterCatalogBusinessException(
      ServletRequest request, MasterCatalogBusinessException ex) {
    String message = messageSource.getMessage(ex.getCode(),
        (ex.getArgs() != null ? ex.getArgs() : new Object[]{}),
        Locale.getDefault());
    HttpServletRequest req = (HttpServletRequest) request;

    log.warn("Master catalog business exception, path= {}, code= {}, message= {} ",
        req.getRequestURI(),
        ex.getCode(),
        ex.getMessage());

    return ResponseEntity.unprocessableEntity()
        .body(Map.of("errorCode", ex.getCode(),
            "message", message)
        );
  }

}
