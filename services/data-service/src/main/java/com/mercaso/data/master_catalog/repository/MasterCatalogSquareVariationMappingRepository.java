package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MasterCatalogSquareVariationMappingRepository extends JpaRepository<MasterCatalogSquareVariationMapping, UUID> {

    @Query(" select m from "
        + "MasterCatalogSquareVariationMapping m "
        + "left join MasterCatalogRawData as r on r.id = m.masterCatalogRawDataId "
        + "where m.variationId in ?1 "
        + "and r.storeId = ?2")
    List<MasterCatalogSquareVariationMapping> findAllByVariationIdInAndStoreIdIs(List<String> catalogObjectIdList, UUID storeId);

    @Query(value = "SELECT m.id, m.variation_id, m.master_catalog_raw_data_id " +
        "FROM master_catalog_square_variation_mapping m " +
        "JOIN master_catalog_raw_data r ON r.id = m.master_catalog_raw_data_id " +
        "WHERE m.variation_id IN :variationIds AND r.store_id = :storeId",
        nativeQuery = true)
    List<Object[]> findByVariationIdsAndStoreIdNative(@Param("variationIds") List<String> variationIds,
        @Param("storeId") UUID storeId);

    @Query(value =
        "select m.variation_id, mc.upc as upc,m.id, m.created_at, m.updated_at, m.master_catalog_raw_data_id "
            + " from master_catalog_square_variation_mapping m "
            + " left join master_catalog_raw_data mc on mc.id = m.master_catalog_raw_data_id where mc.upc in (:upc)",
        nativeQuery = true)
    List<MasterCatalogSquareVariationMapping> findAllByUpcIn(List<String> upc);

    List<MasterCatalogSquareVariationMapping> findAllByMasterCatalogRawDataIdIn(List<UUID> rawDataIds);
}
