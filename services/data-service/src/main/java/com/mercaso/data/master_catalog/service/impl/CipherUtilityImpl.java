package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.config.CipherUtilityConfig;
import com.mercaso.data.master_catalog.service.CipherUtility;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Implementation of CipherUtility service using AES/CBC/PKCS5Padding
 * This implementation includes secure IV handling and proper error management
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CipherUtilityImpl implements CipherUtility {

    // Encryption constants
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int TAG_LENGTH_BIT = 128;
    private static final int IV_LENGTH = 12;

    // Configuration and secure random generator
    private final CipherUtilityConfig cipherUtilityConfig;
    private final SecureRandom secureRandom = new SecureRandom();

    /**
     * Encrypts the input string using AES/GCM/NoPadding
     * Generates a random IV for each encryption
     *
     * @param data The string to encrypt
     * @return Base64 encoded string containing IV and encrypted data
     * @throws RuntimeException if encryption fails
     */
    @Override
    public String encrypt(String data) {
        if (!StringUtils.hasText(data)) {
            log.error("Attempting to encrypt empty or null data");
            throw new IllegalArgumentException("Data to encrypt cannot be empty or null");
        }

        try {
            // Create secret key from configuration
            SecretKeySpec secretKey = new SecretKeySpec(
                    cipherUtilityConfig.getKey().getBytes(StandardCharsets.UTF_8),
                    ALGORITHM);

            // Generate random IV for CBC mode
            byte[] iv = new byte[IV_LENGTH];
            secureRandom.nextBytes(iv);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);

            // Initialize cipher for encryption
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmParameterSpec);

            // Perform encryption
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // Combine IV and encrypted data into single array
            byte[] combined = new byte[iv.length + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryptedBytes, 0, combined, iv.length, encryptedBytes.length);

            // Encode as Base64 string
            return Base64.getEncoder().encodeToString(combined);

        } catch (Exception e) {
            log.error("Encryption failed: ", e);
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Decrypts the input Base64 encoded string using AES/GCM/NoPadding
     * Extracts IV from the input data for decryption
     *
     * @param data Base64 encoded string containing IV and encrypted data
     * @return Decrypted string
     * @throws IllegalArgumentException if input data is invalid
     * @throws RuntimeException         if decryption fails
     */
    @Override
    public String decrypt(String data) {
        if (!StringUtils.hasText(data)) {
            log.error("Attempting to decrypt empty or null data");
            throw new IllegalArgumentException("Data to decrypt cannot be empty or null");
        }

        try {
            // Decode Base64 input
            byte[] combined = Base64.getDecoder().decode(data);
            if (combined.length < IV_LENGTH) {
                throw new IllegalArgumentException("Invalid encrypted data length");
            }

            // Extract IV and encrypted data from combined array
            byte[] iv = new byte[IV_LENGTH];
            byte[] encryptedBytes = new byte[combined.length - IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, IV_LENGTH);
            System.arraycopy(combined, IV_LENGTH, encryptedBytes, 0, encryptedBytes.length);

            // Create secret key from configuration
            SecretKeySpec secretKey = new SecretKeySpec(
                    cipherUtilityConfig.getKey().getBytes(StandardCharsets.UTF_8),
                    ALGORITHM);

            // Initialize cipher for decryption
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new GCMParameterSpec(TAG_LENGTH_BIT, iv));

            // Perform decryption and convert to string
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);

        } catch (IllegalArgumentException e) {
            log.error("Invalid input data for decryption: {}", e.getMessage());
            throw new IllegalArgumentException("Invalid input data", e);
        } catch (Exception e) {
            log.error("Decryption failed: ", e);
            throw new RuntimeException("Decryption failed", e);
        }
    }
}
