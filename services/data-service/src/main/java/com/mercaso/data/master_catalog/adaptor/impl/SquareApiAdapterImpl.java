package com.mercaso.data.master_catalog.adaptor.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.SQUARE_API_LIMIT;
import static com.mercaso.data.master_catalog.enums.InventoryStates.IN_STOCK;
import static com.mercaso.data.master_catalog.enums.InventoryStates.NONE;
import static com.mercaso.data.master_catalog.enums.InventoryStates.RETURNED_BY_CUSTOMER;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryChangesRequestDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryCountsRequestDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveOrdersRequestDto;
import com.mercaso.data.master_catalog.dto.square.InventoryChangeDto;
import com.mercaso.data.master_catalog.dto.square.InventoryCountDto;
import com.mercaso.data.master_catalog.dto.square.OrdersDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsRequestDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsResponseDto;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.mercaso.data.master_catalog.enums.square.InventoryChangeTypes;
import com.mercaso.data.master_catalog.mapper.MasterCatalogInventoryChangesMapper;
import com.mercaso.data.master_catalog.mapper.MasterCatalogInventoryCountMapper;
import com.mercaso.data.master_catalog.mapper.MasterCatalogOrderMapper;
import com.mercaso.data.master_catalog.mapper.SquareLocationMapper;
import com.mercaso.data.master_catalog.mapper.SquareObtainTokenResponseMapper;
import com.mercaso.data.master_catalog.mapper.SquareSearchCatalogObjectsRequestMapper;
import com.mercaso.data.master_catalog.service.SquareClientService;
import com.squareup.square.SquareClient;
import com.squareup.square.exceptions.ApiException;
import com.squareup.square.models.BatchChangeInventoryRequest;
import com.squareup.square.models.BatchChangeInventoryResponse;
import com.squareup.square.models.BatchRetrieveInventoryChangesRequest;
import com.squareup.square.models.BatchRetrieveInventoryChangesResponse;
import com.squareup.square.models.BatchRetrieveInventoryCountsRequest;
import com.squareup.square.models.BatchRetrieveInventoryCountsResponse;
import com.squareup.square.models.CatalogObject;
import com.squareup.square.models.InventoryAdjustment;
import com.squareup.square.models.InventoryChange;
import com.squareup.square.models.ListLocationsResponse;
import com.squareup.square.models.ObtainTokenRequest;
import com.squareup.square.models.ObtainTokenResponse;
import com.squareup.square.models.SearchCatalogObjectsResponse;
import com.squareup.square.models.SearchOrdersRequest;
import com.squareup.square.models.SearchOrdersResponse;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class SquareApiAdapterImpl implements SquareApiAdapter {

    private static final String GRANT_TYPE_AUTHORIZATION_CODE = "authorization_code";
    private static final String GRANT_TYPE_REFRESH_TOKEN = "refresh_token";

    private final SquareObtainTokenResponseMapper squareObtainTokenResponseMapper;

    private final SquareClientService squareClientService;

    private final SquareSearchCatalogObjectsRequestMapper squareSearchCatalogObjectsRequestMapper;

    private final MasterCatalogInventoryCountMapper masterCatalogInventoryCountMapper;

    private final MasterCatalogInventoryChangesMapper masterCatalogInventoryChangesMapper;

    private final MasterCatalogOrderMapper masterCatalogOrderMapper;

    private final SquareLocationMapper squareLocationMapper;

    @Override
    public SquareObtainTokenResponse obtainTokenByAuthorizationCode(String code,
        String applicationId,
        String applicationSecret) {
        try {
            ObtainTokenResponse obtainTokenResponse = squareClientService.getDefaultSquareClient()
                .getOAuthApi()
                .obtainToken(buildAuthorizationCodeRequest(code, applicationId, applicationSecret));

            return squareObtainTokenResponseMapper.from(obtainTokenResponse);
        } catch (ApiException | IOException e) {
            log.error("Failed to obtain token by authorization code", e);
            throw new RuntimeException("Failed to obtain token by authorization code", e);
        }
    }

    @Override
    public SquareObtainTokenResponse obtainTokenByRefreshToken(String refreshToken,
        String applicationId,
        String applicationSecret) {
        try {
            ObtainTokenResponse obtainTokenResponse = squareClientService.getDefaultSquareClient()
                .getOAuthApi()
                .obtainToken(
                    buildRefreshTokenRequest(refreshToken, applicationId, applicationSecret));

            return squareObtainTokenResponseMapper.from(obtainTokenResponse);
        } catch (ApiException | IOException e) {
            log.error("Failed to obtain token by refresh token", e);
            throw new RuntimeException("Failed to obtain token by refresh token", e);
        }
    }

    @Override
    public SearchCatalogObjectsResponseDto searchCatalogObjects(UUID storeId,
        SearchCatalogObjectsRequestDto request) {
        List<CatalogObject> objects = new ArrayList<>();
        List<CatalogObject> relatedObjects = new ArrayList<>();
        String cursor = null;

        SquareClient squareClient = squareClientService.getSquareClient(storeId);

        do {
            try {
                request.setCursor(cursor);

                SearchCatalogObjectsResponse response = squareClient.getCatalogApi()
                    .searchCatalogObjects(
                        squareSearchCatalogObjectsRequestMapper.toExternalDto(request));

                if (response.getErrors() != null && !response.getErrors().isEmpty()) {
                    response.getErrors().forEach(
                        error -> log.error("Call square client, error: {}", error.getDetail()));
                    break;
                }

                Optional.ofNullable(response.getObjects()).ifPresent(objects::addAll);
                Optional.ofNullable(response.getRelatedObjects()).ifPresent(relatedObjects::addAll);
                cursor = response.getCursor();
            } catch (ApiException | IOException e) {
                throw new RuntimeException("Failed to fetch items from Square", e);
            }
        } while (cursor != null);

        return new SearchCatalogObjectsResponseDto(objects, relatedObjects);
    }

    @Override
    public List<InventoryCountDto> batchRetrieveInventoryCounts(UUID storeId,
        BatchRetrieveInventoryCountsRequestDto request) {
        SquareClient squareClient = squareClientService.getSquareClient(storeId);
        List<InventoryCountDto> inventoryCounts = new ArrayList<>();
        String cursor = null;

        BatchRetrieveInventoryCountsRequest.Builder requestBuilder = new BatchRetrieveInventoryCountsRequest.Builder()
            .locationIds(request.getLocationIds())
            .updatedAfter(request.getUpdatedAfter())
            .limit(request.getLimit());

        do {
            try {
                if (cursor != null) {
                    requestBuilder.cursor(cursor);
                }

                BatchRetrieveInventoryCountsResponse response = squareClient.getInventoryApi()
                    .batchRetrieveInventoryCounts(requestBuilder.build());

                if (response.getErrors() != null && !response.getErrors().isEmpty()) {
                    response.getErrors().forEach(
                        error -> log.error("Call square client, error: {}", error.getDetail()));
                    break;
                }

                Optional.ofNullable(response.getCounts())
                    .ifPresent(counts -> counts.stream()
                        .filter(inventoryCount -> !inventoryCount.getState()
                            .equals(RETURNED_BY_CUSTOMER.name()))
                        .map(masterCatalogInventoryCountMapper::fromExternalDto)
                        .forEach(
                            inventoryCounts::add));

                cursor = response.getCursor();

            } catch (ApiException | IOException e) {
                throw new RuntimeException("Failed to fetch inventory counts from Square", e);
            }
        } while (cursor != null);

        return inventoryCounts;
    }


    @Override
    public List<InventoryChangeDto> batchRetrieveInventoryChanges(UUID storeId,
        BatchRetrieveInventoryChangesRequestDto request) {
        SquareClient squareClient = squareClientService.getSquareClient(storeId);
        List<InventoryChangeDto> inventoryChangeDtoList = new ArrayList<>();
        String cursor = null;
        BatchRetrieveInventoryChangesRequest.Builder requestBuilder = buildInitialRequest(request);

        while (true) {
            try {
                BatchRetrieveInventoryChangesRequest currentRequest = buildRequestWithCursor(
                    requestBuilder, cursor);
                BatchRetrieveInventoryChangesResponse response = squareClient.getInventoryApi()
                    .batchRetrieveInventoryChanges(currentRequest);

                if (hasErrors(response)) {
                    logErrors(response);
                    break;
                }

                addInventoryChanges(response.getChanges(), inventoryChangeDtoList);
                cursor = response.getCursor();

                if (cursor == null) {
                    break;
                }

            } catch (ApiException | IOException e) {
                log.error("Failed to fetch inventory changes from Square", e);
                throw new RuntimeException("Failed to fetch inventory changes from Square", e);
            }
        }

        return inventoryChangeDtoList;
    }

    @Override
    public List<OrdersDto> batchRetrieveOrders(UUID storeId,
        BatchRetrieveOrdersRequestDto request) {
        SquareClient squareClient = squareClientService.getSquareClient(storeId);
        List<OrdersDto> OrdersDtoList = new ArrayList<>();
        String cursor = null;
        SearchOrdersRequest.Builder requestBuilder = new SearchOrdersRequest.Builder()
            .locationIds(request.getLocationIds())
            .limit(SQUARE_API_LIMIT)
            .query(request.getQuery());
        do {
            try {
                if (cursor != null) {
                    requestBuilder.cursor(cursor);
                }
                SearchOrdersResponse response = squareClient.getOrdersApi()
                    .searchOrders(requestBuilder.build());

                if (response.getErrors() != null && !response.getErrors().isEmpty()) {
                    response.getErrors().forEach(
                        error -> log.error("Call square client, error: {}", error.getDetail()));
                    break;
                }

                Optional.ofNullable(response.getOrders())
                    .ifPresent(orders -> orders.stream()
                        .map(masterCatalogOrderMapper::fromExternalDto)
                        .forEach(orderDto -> {
                                orderDto.setOrderId(String.valueOf(UUID.randomUUID()));
                                OrdersDtoList.add(orderDto);
                            }
                        ))
                ;
                log.info("Synchronized orders, downloading orders data with cursor {}", cursor);
                cursor = response.getCursor();

            } catch (ApiException | IOException e) {
                throw new RuntimeException("Failed to fetch orders from Square", e);
            }
        } while (cursor != null);

        return OrdersDtoList;
    }

    @Override
    public List<MasterCatalogLocationDto> listLocations(UUID storeId) {
        SquareClient squareClient = squareClientService.getSquareClient(storeId);
        List<MasterCatalogLocationDto> locations = new ArrayList<>();

        try {

            ListLocationsResponse response = squareClient.getLocationsApi()
                .listLocations();

            if (response.getErrors() != null && !response.getErrors().isEmpty()) {
                response.getErrors().forEach(
                    error -> log.error("Call square client, error: {}", error.getDetail()));
            }

            response.getLocations().stream()
                .map(squareLocationMapper::from)
                .filter(Objects::nonNull)
                .forEach(locations::add);

        } catch (ApiException | IOException e) {
            throw new RuntimeException("Failed to fetch locations from Square", e);
        }

        return locations;
    }

    @Override
    public void batchChangeInventory(UUID storeId, List<String> locationIds, Map<String, Integer> changes) {

        if (storeId == null) {
            log.warn("Batch change inventory store ID is null");
            return;
        }

        log.info("Batch change inventory for store: {}, number of items: {}",
            storeId, changes.size());

        if (CollectionUtils.isEmpty(changes)) {
            log.warn("Batch change inventory for store: {}, no valid inventory changes to process", storeId);
            return;
        }

        if (CollectionUtils.isEmpty(locationIds)) {
            log.warn("Batch change inventory for store: {},no valid location IDs to process", storeId);
            return;
        }

        SquareClient squareClient = squareClientService.getSquareClient(storeId);

        locationIds.forEach(locationId -> {

            List<InventoryAdjustment> adjustmentList = new ArrayList<>();

            // Add all changes to the list
            changes.forEach((catalogObjectId, quantityDelta) -> {
                InventoryAdjustment adjustment = new InventoryAdjustment.Builder()
                    .catalogObjectId(catalogObjectId)
                    .quantity(String.valueOf(quantityDelta))
                    .fromState(NONE.name())
                    .toState(IN_STOCK.name())
                    .occurredAt(Instant.now().toString())
                    .locationId(locationId)
                    .build();

                adjustmentList.add(adjustment);
            });

            String idempotencyKey = UUID.randomUUID().toString();
            BatchChangeInventoryRequest request = new BatchChangeInventoryRequest.Builder(idempotencyKey)
                .idempotencyKey(idempotencyKey)
                .changes(adjustmentList.stream()
                    .map(adjustment -> {
                        return new InventoryChange.Builder()
                            .adjustment(adjustment)
                            .type(InventoryChangeTypes.ADJUSTMENT.name())
                            .build();
                    })
                    .collect(Collectors.toList()))
                .build();

            try {
                BatchChangeInventoryResponse batchChangeInventoryResponse = squareClient.getInventoryApi()
                    .batchChangeInventory(request);

                if (batchChangeInventoryResponse.getErrors() != null && !batchChangeInventoryResponse.getErrors().isEmpty()) {
                    throw new RuntimeException(
                        "Call square api, Failed to batch change inventory to Square: {}"
                            + batchChangeInventoryResponse.getErrors());
                }

                log.info("Batch change inventory for store: {}, location: {}, number of items: {}",
                    storeId,
                    locationId,
                    changes.size());

            } catch (ApiException e) {
                throw new RuntimeException("Call square api, batch change inventory to Square, ApiException: ", e);
            } catch (IOException e) {
                throw new RuntimeException("Call square api, batch change inventory to Square, IOException: ", e);
            }
        });

    }

    private BatchRetrieveInventoryChangesRequest.Builder buildInitialRequest(
        BatchRetrieveInventoryChangesRequestDto request) {
        return new BatchRetrieveInventoryChangesRequest.Builder()
            .locationIds(request.getLocationIds())
            .types(request.getTypes())
            .limit(request.getLimit())
            .updatedAfter(request.getUpdatedAfter());
    }

    private BatchRetrieveInventoryChangesRequest buildRequestWithCursor(
        BatchRetrieveInventoryChangesRequest.Builder builder,
        String cursor) {
        if (cursor != null) {
            builder.cursor(cursor);
        }
        return builder.build();
    }

    private boolean hasErrors(BatchRetrieveInventoryChangesResponse response) {
        return response.getErrors() != null && !response.getErrors().isEmpty();
    }

    private void logErrors(BatchRetrieveInventoryChangesResponse response) {
        response.getErrors().forEach(error -> log.error("Square API Error: {}", error.getDetail()));
    }

    private void addInventoryChanges(List<InventoryChange> changes,
        List<InventoryChangeDto> targetList) {
        if (CollectionUtils.isEmpty(changes)) {
            return;
        }
        changes.stream()
            .map(masterCatalogInventoryChangesMapper::fromExternalDto)
            .filter(Objects::nonNull)
            .forEach(targetList::add);
    }

    private ObtainTokenRequest buildAuthorizationCodeRequest(String code, String applicationId,
        String applicationSecret) {
        return new ObtainTokenRequest(applicationId, GRANT_TYPE_AUTHORIZATION_CODE,
            applicationSecret,
            code, null, null, null, null, null, null);
    }

    private ObtainTokenRequest buildRefreshTokenRequest(String refreshToken, String applicationId,
        String applicationSecret) {
        return new ObtainTokenRequest(applicationId, GRANT_TYPE_REFRESH_TOKEN, applicationSecret,
            null, null, refreshToken, null, null, null, null);
    }
}
