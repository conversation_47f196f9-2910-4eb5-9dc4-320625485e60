package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationInProgressPayload;

@EventPublishConfig(
    publishLocal = true
)
public class RemoveDuplicationInProgressEvent extends
    BaseApplicationEvent<RemoveDuplicationInProgressPayload> {

    public RemoveDuplicationInProgressEvent(Object source,
        RemoveDuplicationInProgressPayload payload) {
        super(source, payload);
    }
}
