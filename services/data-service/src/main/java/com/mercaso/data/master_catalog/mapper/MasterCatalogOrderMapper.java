package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.square.OrdersDto;
import com.squareup.square.models.Order;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MasterCatalogOrderMapper {
    Order toExternalDto(OrdersDto dto);

    OrdersDto fromExternalDto(Order externalDto);
}
