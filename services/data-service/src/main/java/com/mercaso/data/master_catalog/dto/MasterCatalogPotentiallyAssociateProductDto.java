package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
@Builder
@NoArgsConstructor
public class MasterCatalogPotentiallyAssociateProductDto extends BaseDto {

    private UUID id;
    private UUID productId;
    private String productName;
    private String productUpc;
    private List<String> productImages;
    private UUID potentiallyAssociateProductId;
    private String potentiallyAssociateProductName;
    private String potentiallyAssociateProductUpc;
    private PotentiallyAssociateProductStatus status;
    private UUID taskId;
    private UUID jobId;
    private Boolean associated;
    private String createdBy;
    private String updatedBy;
    private Instant createdAt;
    private Instant updatedAt;
    private List<String> potentiallyAssociateProductImages;
}
