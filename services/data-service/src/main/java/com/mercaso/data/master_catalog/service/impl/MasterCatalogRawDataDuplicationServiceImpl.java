package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.DUPLICATE_FIELD_NAME_DESCRIPTION;
import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.DUPLICATE_FIELD_NAME_SKU;
import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.DUPLICATE_FIELD_NAME_UPC;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class MasterCatalogRawDataDuplicationServiceImpl implements MasterCatalogRawDataDuplicationService {

    private final MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    private static final List<String> DUPLICATE_FIELDS = List.of(
        DUPLICATE_FIELD_NAME_UPC,
        DUPLICATE_FIELD_NAME_DESCRIPTION,
        DUPLICATE_FIELD_NAME_SKU
    );
    
    /**
     * Merge duplication records into a single group, the single group is the one with the earliest created duplication record.
     * 
     * @param duplicationRecords the duplication records to merge
     * @return the UUID is the duplication group of the merged duplication records with the earliest created duplication record
     */
    @Override
    public UUID mergeDuplicationRecords(List<MasterCatalogRawDataDuplication> duplicationRecords) {
        // If just one group, skip merging, return the group directly'
        // Group by duplication group
        if (CollectionUtils.isEmpty(duplicationRecords)) {
            return null;
        }

        Map<UUID, List<MasterCatalogRawDataDuplication>> groupByDuplicationGroup = duplicationRecords.stream()
            .collect(Collectors.groupingBy(MasterCatalogRawDataDuplication::getDuplicationGroup));

        if (groupByDuplicationGroup.size() == 1) {
            return groupByDuplicationGroup.keySet().iterator().next();
        }

        // Get duplication group of the earliest created duplication record
        UUID duplicationGroup = duplicationRecords.stream()
            .min(Comparator.comparing(MasterCatalogRawDataDuplication::getCreatedAt))
            .map(MasterCatalogRawDataDuplication::getDuplicationGroup)
                .orElse(null);
            
        duplicationRecords = duplicationRecords.stream()
            .filter(duplication -> !duplication.getDuplicationGroup().equals(duplicationGroup))
            .toList();

        log.info("Merging {} duplication records into group {}", duplicationRecords.size(), duplicationGroup);
        duplicationRecords.forEach(duplication -> {
            duplication.setDuplicationGroup(duplicationGroup);
            duplication.setUpdatedAt(Instant.now());
        });

        if (CollectionUtils.isNotEmpty(duplicationRecords)) {
            masterCatalogRawDataDuplicationRepository.saveAll(duplicationRecords);
        }

        return duplicationGroup;
    }
}
