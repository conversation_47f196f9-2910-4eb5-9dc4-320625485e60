package com.mercaso.data.master_catalog.event.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface EventPublishConfig {

    /**
     * Whether to persist in database (default true)
     */
    boolean persist() default true;

    /**
     * Whether to publish local application event (default false)
     */
    boolean publishLocal() default false;
} 