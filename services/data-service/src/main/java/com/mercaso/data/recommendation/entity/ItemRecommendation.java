package com.mercaso.data.recommendation.entity;

import java.math.BigDecimal;

import com.mercaso.data.master_catalog.entity.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "item_recommendation")
public class ItemRecommendation extends BaseEntity {

  @Size(max = 255)
  @Column(name = "store_id")
  private String storeId;

  @Size(max = 255)
  @Column(name = "sku_number")
  private String skuNumber;

  @Size(max = 255)
  @Column(name = "sku_name")
  private String skuName;

  @Size(max = 255)
  @Column(name = "product_id")
  private String productId;

  @Size(max = 255)
  @Column(name = "department")
  private String department;

  @Size(max = 255)
  @Column(name = "reason")
  private String reason;

  @Size(max = 255)
  @Column(name = "reason_value")
  private String reasonValue;

  @Size(max = 255)
  @Column(name = "version")
  private String version;

  @Size(max = 64)
  @Column(name = "department_id")
  private String departmentId;

  @Size(max = 255)
  @Column(name = "high_price")
  private String highPrice;

  @Size(max = 255)
  @Column(name = "low_price")
  private String lowPrice;

  @Size(max = 255)
  @Column(name = "avg_price")
  private String avgPrice;

  @Column(name = "cost", precision = 10, scale = 2)
  private BigDecimal cost;
}
