package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface MasterCatalogBatchJobRepository extends JpaRepository<MasterCatalogBatchJob, UUID>,
    JpaSpecificationExecutor<MasterCatalogBatchJob> {

    boolean existsAllByStatusIsNot(@NotNull MasterCatalogBatchJobStatus status);

    List<MasterCatalogBatchJob> findAllByStatusIn(Collection<MasterCatalogBatchJobStatus> statuses);
}