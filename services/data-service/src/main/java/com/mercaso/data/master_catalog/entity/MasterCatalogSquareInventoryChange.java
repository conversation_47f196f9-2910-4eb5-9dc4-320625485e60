package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_square_inventory_change")
public class MasterCatalogSquareInventoryChange extends BaseEntity {

    @Column(name = "master_catalog_raw_data_id")
    private UUID masterCatalogRawDataId;

    @Size(max = 128)
    @Column(name = "type", length = 128)
    private String type;

    @Size(max = 255)
    @Column(name = "from_state")
    private String fromState;

    @Size(max = 255)
    @Column(name = "to_state")
    private String toState;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "occurred_at")
    private Instant occurredAt;

    @Size(max = 255)
    @Column(name = "source_id")
    private String sourceId;

}
