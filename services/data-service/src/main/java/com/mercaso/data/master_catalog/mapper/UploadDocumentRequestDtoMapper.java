package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.external.UploadDocumentRequestDto;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface UploadDocumentRequestDtoMapper {

    UploadDocumentRequest toExternalDto(UploadDocumentRequestDto dto);

    UploadDocumentRequestDto fromExternalDto(UploadDocumentRequest dto);
}
