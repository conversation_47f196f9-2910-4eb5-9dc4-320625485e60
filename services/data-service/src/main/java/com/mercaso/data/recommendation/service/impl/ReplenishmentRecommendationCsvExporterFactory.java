package com.mercaso.data.recommendation.service.impl;

import com.mercaso.data.recommendation.service.ReplenishmentRecommendationCsvExporter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReplenishmentRecommendationCsvExporterFactory {

    private final Map<String, ReplenishmentRecommendationCsvExporter> exporterMap = new HashMap<>();
    private final DefaultReplenishmentRecommendationCsvExporter defaultExporter;

    @Autowired
    public ReplenishmentRecommendationCsvExporterFactory(List<ReplenishmentRecommendationCsvExporter> exporters,
        DefaultReplenishmentRecommendationCsvExporter defaultExporter) {
        for (ReplenishmentRecommendationCsvExporter exporter : exporters) {
            String className = exporter.getClass().getSimpleName();
            if (className.endsWith("ReplenishmentRecommendationCsvExporter")) {
                String type = className.replace("ReplenishmentRecommendationCsvExporter", "").toUpperCase();
                if (!"default".equals(type)) {
                    exporterMap.put(type, exporter);
                }
            }
        }
        this.defaultExporter = defaultExporter;
    }

    public ReplenishmentRecommendationCsvExporter getExporter(String integrationType) {
        return exporterMap.getOrDefault(integrationType.toUpperCase(), defaultExporter);
    }
}
