package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import java.util.List;
import java.util.UUID;

public interface MasterCatalogGeneratePotentialDuplicationService {

    public List<List<UUID>> generatePotentialDuplciationInBatch(List<UUID> uuidList,
        String indexName,
        Float threshold);

    public List<List<UUID>> generatePotentialDuplicationWithProduct(List<UUID> uuidList,
        String indexName,
        Float threshold);

    public void pushProduct(List<MasterCatalogProduct> productList,
        String indexName);

}
