package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface MasterCatalogProductAssociationRepository extends JpaRepository<MasterCatalogProductAssociation, UUID>,
    JpaSpecificationExecutor<MasterCatalogProductAssociation> {

    List<MasterCatalogProductAssociation> findAllByUpcIn(List<String> upc);

    List<MasterCatalogProductAssociation> findByAssociationGroup(UUID associationGroup);

    List<MasterCatalogProductAssociation> findByAssociationGroupIn(Collection<UUID> associationGroups);
}
