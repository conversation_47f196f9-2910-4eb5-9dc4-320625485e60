package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface MasterCatalogPotentiallyAssociateProductService {

  Page<MasterCatalogPotentiallyAssociateProductDto> search(UUID taskId, PotentiallyAssociateProductStatus status, PageRequest pageRequest);

  MasterCatalogPotentiallyAssociateProductDto update(UUID id, boolean associated);

  List<MasterCatalogPotentiallyAssociateProductDto> processPotentiallyAssociateProduct(UUID jobId, List<List<UUID>> productIds);

  void submit(List<UUID> potentiallyAssociateProductIds);
}
