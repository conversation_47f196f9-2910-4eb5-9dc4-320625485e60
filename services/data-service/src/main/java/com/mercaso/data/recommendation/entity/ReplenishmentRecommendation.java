package com.mercaso.data.recommendation.entity;

import com.mercaso.data.master_catalog.entity.BaseEntity;
import jakarta.persistence.*;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "replenishment_recommendation")
@Builder
public class ReplenishmentRecommendation extends BaseEntity {

    @Column(name = "store_id", nullable = false)
    private UUID storeId;

    @Column(name = "sku", length = 32)
    private String sku;

    @Column(name = "name")
    private String name;

    @Column(name = "recommended_quantity", nullable = false, columnDefinition = "numeric")
    private BigDecimal recommendedQuantity;

    @Column(name = "next_order_time")
    private Instant nextOrderTime;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    @Column(name = "batch_number")
    private String batchNumber;

    @Column(name = "upc", length = 64)
    private String upc;

    @Column(name = "product_id")
    private String productId;

    @Column(name = "last_order_time")
    private Instant lastOrderTime;

    @Column(name = "model_type")
    private String modelType;

    @Column(name = "reason")
    private String reason;

    @Column(name = "last_order_quantity")
    private Integer lastOrderQuantity;

    @Column(name = "department_id")
    private UUID departmentId;

    @Column(name = "department_name")
    private String departmentName;

    @Column(name = "recent_order_count")
    private Integer recentOrderCount;
}