package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.mapper.MasterCatalogProductMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogProductService;
import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class MasterCatalogProductServiceImpl implements MasterCatalogProductService {

    private final MasterCatalogProductRepository masterCatalogProductRepository;
    private final MasterCatalogProductMapper masterCatalogProductMapper;
    private final MasterCatalogImageRepository masterCatalogImageRepository;
    private final ExternalApiAdapter externalApiAdapter;
    private final MasterCatalogProductAssociationRepository masterCatalogProductAssociationRepository;
    private final S3OperationAdapter s3OperationAdapter;

    @Override
    public Page<MasterCatalogProductDto> searchAssociatedProducts(String upc, String description, Pageable pageable) {
        if (StringUtils.isNotBlank(upc)) {
            return searchByUpc(upc, pageable);
        }

        if (StringUtils.isNotBlank(description)) {
            return searchByDescription(description, pageable);
        }

        return Page.empty();
    }

    private Page<MasterCatalogProductDto> searchByUpc(String upc, Pageable pageable) {
        return findAssociatedProductsByUpcs(Collections.singletonList(upc), pageable);
    }

    private Page<MasterCatalogProductDto> searchByDescription(String description, Pageable pageable) {
        List<String> upcs = externalApiAdapter.getUpcsFromExternalApi(description);
        return findAssociatedProductsByUpcs(upcs, pageable);
    }

    @Override
    public Page<MasterCatalogProductDto> searchProducts(String upc, String name, Pageable pageable) {

        Specification<MasterCatalogProduct> specification = ((root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.isNotBlank(upc)) {
                predicates.add(builder.equal(root.get("upc"), upc));
            }
            if (StringUtils.isNotBlank(name)) {
                predicates.add(builder.like(root.get("name"),
                    "%" + name + "%"
                ));
            }

            query.orderBy(builder.desc(root.get("createdAt")));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        Page<MasterCatalogProduct> productPage = masterCatalogProductRepository.findAll(specification, pageable);
        return mapProductsWithImages(productPage);
    }

    private Page<MasterCatalogProductDto> findAssociatedProductsByUpcs(List<String> upcs, Pageable pageable) {
        if (CollectionUtils.isEmpty(upcs)) {
            log.info("No UPCs provided for association lookup");
            return Page.empty();
        }

        // Get associations directly in one query
        List<MasterCatalogProductAssociation> productAssociations = masterCatalogProductAssociationRepository.findAllByUpcIn(
          upcs);
        if (CollectionUtils.isEmpty(productAssociations)) {
            log.info("No association found for upc: {}", upcs);
            return Page.empty();
        }

        List<UUID> associationGroups = filterGroups(productAssociations);
        if (CollectionUtils.isEmpty(associationGroups)) {
            log.info("No association groups found for given UPCs: {}", upcs);
            return Page.empty();
        }

        // Get all associated UPCs in one go
        List<String> associatedUpcs = findUpcsByAssociationGroups(associationGroups);
        if (CollectionUtils.isEmpty(associatedUpcs)) {
            log.info("No UPCs found for association groups: {}", associationGroups);
            return Page.empty();
        }

        // Remove the original UPCs from the associated UPCs
        associatedUpcs.removeAll(upcs);

        return findProductsByUpcs(associatedUpcs, pageable);
    }

    private List<UUID> filterGroups(List<MasterCatalogProductAssociation> productAssociations) {
        return productAssociations.stream()
          .map(MasterCatalogProductAssociation::getAssociationGroup)
          .distinct()
          .toList();
    }

    private List<String> findUpcsByAssociationGroups(List<UUID> associationGroups) {
        Specification<MasterCatalogProductAssociation> specForAssociation = (root, query, cb) -> root.get("associationGroup")
          .in(associationGroups);
        List<MasterCatalogProductAssociation> associations = masterCatalogProductAssociationRepository.findAll(
          specForAssociation);
        return associations.stream().map(MasterCatalogProductAssociation::getUpc).distinct().collect(Collectors.toList());
    }

    private Page<MasterCatalogProductDto> findProductsByUpcs(List<String> upcs, Pageable pageable) {
        if (CollectionUtils.isEmpty(upcs)) {
            log.info("No UPCs to search for products");
            return Page.empty();
        }

        Specification<MasterCatalogProduct> spec = (root, query, cb) -> root.get("upc").in(upcs);
        Page<MasterCatalogProduct> products = masterCatalogProductRepository.findAll(spec, pageable);
        return mapProductsWithImages(products);
    }

    private Page<MasterCatalogProductDto> mapProductsWithImages(Page<MasterCatalogProduct> products) {
        if (products.isEmpty()) {
            log.info("No products found");
            return Page.empty();
        }

        List<UUID> rawDataIds = filterRawDataIdsFrom(products);
        if (CollectionUtils.isEmpty(rawDataIds)) {
            log.info("No raw data IDs found in products");
            return Page.empty();
        }

        log.info("rawDataIds: {}", rawDataIds);

        List<MasterCatalogImage> allImages = masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(rawDataIds);
        Map<UUID, List<String>> imageMap = buildImageMap(allImages);

        return products.map(product -> {
            MasterCatalogProductDto dto = masterCatalogProductMapper.toDto(product);
            dto.setImages(imageMap.getOrDefault(product.getMasterCatalogRawDataId(), Collections.emptyList()));
            return dto;
        });
    }

    private Map<UUID, List<String>> buildImageMap(List<MasterCatalogImage> allImages) {
        return allImages.stream()
          .peek(image -> image.setImagePath(s3OperationAdapter.getSignedUrl(image.getImagePath())))
          .collect(Collectors.groupingBy(MasterCatalogImage::getMasterCatalogRawDataId,
            Collectors.mapping(MasterCatalogImage::getImagePath, Collectors.toList())));
    }

    private List<UUID> filterRawDataIdsFrom(Page<MasterCatalogProduct> products) {
        return products.stream()
          .map(MasterCatalogProduct::getMasterCatalogRawDataId)
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
    }
}
