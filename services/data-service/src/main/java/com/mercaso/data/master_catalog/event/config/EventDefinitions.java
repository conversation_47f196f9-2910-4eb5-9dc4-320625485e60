package com.mercaso.data.master_catalog.event.config;

import com.mercaso.data.master_catalog.event.applicationevent.BusinessApplicationEventType;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventType;
import com.mercaso.data.master_catalog.event.core.EventTypeBuilder;
import com.mercaso.data.master_catalog.event.core.EventTypeRegistry;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * Centralize the configuration of all event definitions
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class EventDefinitions {

    private final EventTypeBuilder eventTypeBuilder;
    private final EventTypeRegistry eventTypeRegistry;

    @PostConstruct
    public void registerEventTypes() {
        for (BusinessApplicationEventType enumType : BusinessApplicationEventType.values()) {
            registerEventType(enumType);
        }
    }

    private void registerEventType(BusinessApplicationEventType enumType) {
        log.info("Registering event type: {} with event class: {}",
            enumType.name(),
            enumType.getApplicationEventClass().getName());
        ApplicationEventType<?, ?> applicationEventType = eventTypeBuilder.newEvent(enumType.getEventTypeName())
            .withPayload(enumType.getPayloadClass())
            .build(enumType.getApplicationEventClass());

        eventTypeRegistry.register(applicationEventType);
    }
}
