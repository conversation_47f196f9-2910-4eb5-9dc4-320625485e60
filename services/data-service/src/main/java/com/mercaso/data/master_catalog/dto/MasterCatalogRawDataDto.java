package com.mercaso.data.master_catalog.dto;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogRawData}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterCatalogRawDataDto extends BaseDto {

    UUID id;
    UUID storeId;
    String upc;
    String name;
    String description;
    String brand;
    String skuNumber;
    String department;
    String category;
    String subCategory;
    String clazz;
    String primaryVendor;
    private String reviewedBy;
    private Instant reviewedAt;
    List<String> images;
}
