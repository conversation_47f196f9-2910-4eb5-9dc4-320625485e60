package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogLocationRepository extends JpaRepository<MasterCatalogLocation, UUID> {

    List<MasterCatalogLocation> findAllByStoreId(UUID storeId);
}