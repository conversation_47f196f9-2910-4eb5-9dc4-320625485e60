package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationSubmitPayload;

@EventPublishConfig(
    publishLocal = true
)
public class RemoveDuplicationSubmitEvent extends BaseApplicationEvent<RemoveDuplicationSubmitPayload> {

    public RemoveDuplicationSubmitEvent(Object source, RemoveDuplicationSubmitPayload payload) {
        super(source, payload);
    }
} 