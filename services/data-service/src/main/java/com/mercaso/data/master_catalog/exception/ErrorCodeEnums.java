package com.mercaso.data.master_catalog.exception;

public enum ErrorCodeEnums {

  COMMON_CODE("000000"),

  //Master Catalog Raw data
  MASTER_CATALOG_RAW_DATA_NOT_FOUND("001001"),
  MASTER_CATALOG_BATCH_JOB_NOT_FOUND("001002"),
  MASTER_CATALOG_TASK_NOT_FOUND("001003"),
  MASTER_CATALOG_POTENTIALLY_ASSOCIATION_PRODUCT_NOT_FOUND("001004"),
  MASTER_CATALOG_BATCH_JOB_NOT_AVAILABLE("001005"),
  MASTER_CATALOG_PRODUCT_ALREADY_REVIEWED("001006"),
  FOUND_NON_OR_MULTIPLE_TASK("001007"),
  UPC_FOUND_IN_TWO_DIFFERENT_ASSOCIATION_GROUPS("001008"),
  MC_UPLOAD_IMAGE_FAILURE("001009"),
  MC_UPLOAD_IMAGE_EMPTY("001010"),
  MC_UPLOAD_IMAGE_INVALID_NAME("001011"),
  MC_UPLOAD_IMAGE_INVALID_TYPE("001012"),
  NO_ENOUGH_PERMISSION("001013"),

  //Image Management
  IMAGE_MANAGEMENT_ITEM_IMAGE_NOT_FOUND("002000"),

  IMAGE_MANAGEMENT_ITEM_IMAGE_UPLOAD_ALREADY("002001"),

  UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE("002002"),

  INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_FILE_NAME("002003"),

  UPLOAD_IMAGE_MANAGEMENT_ITEM_IMAGE_FAILURE("002004"),

  INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_SHOT_AT("002005"),

  //Unexpected error
  UNEXPECTED_ERROR("020001");


  private String code;

  ErrorCodeEnums(String code) {
    this.code = code;
  }

  public String getCode() {
    return code;
  }
}
