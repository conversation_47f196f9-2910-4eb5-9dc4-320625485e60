package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.square.InventoryCountDto;
import com.squareup.square.models.InventoryCount;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogInventoryCountMapper {

    InventoryCount toExternalDto(InventoryCountDto dto);

    InventoryCountDto fromExternalDto(InventoryCount externalDto);

}
