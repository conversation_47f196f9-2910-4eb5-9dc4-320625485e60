package com.mercaso.data.master_catalog.event.applicationevent.publisher;

import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.event.applicationevent.BusinessApplicationEventType;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventPublisher;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsCompletedPayload;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsInProgressPayload;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsSubmitPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationInProgressPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationSubmitPayload;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataUpdatedPayload;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataSyncedPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class ApplicationEventPublisherProvider {

    private final ApplicationEventPublisher applicationEventPublisher;
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;

    public void removeDuplicationSubmitEvent(MasterCatalogBatchJob job,
            List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit) {
        Set<UUID> validPotentiallyDuplicateIds = validToSubmit.stream()
                .map(MasterCatalogPotentiallyDuplicateRawData::getId)
                .collect(Collectors.toSet());

        RemoveDuplicationSubmitPayload payload = RemoveDuplicationSubmitPayload.builder()
                .batchJobId(job.getId())
                .potentiallyDuplicateIds(new ArrayList<>(validPotentiallyDuplicateIds))
                .payload(masterCatalogBatchJobMapper.toDto(job))
                .build();

        applicationEventPublisher.publish(BusinessApplicationEventType.REMOVE_DUPLICATION_SUBMIT, payload);
    }

    public void generateProductsSubmitEvent(MasterCatalogBatchJob job,
            List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit) {
        Set<UUID> validPotentiallyDuplicateIds = validToSubmit.stream()
                .map(MasterCatalogPotentiallyDuplicateRawData::getId)
                .collect(Collectors.toSet());

        GenerateProductsSubmitPayload payload = GenerateProductsSubmitPayload.builder()
                .batchJobId(job.getId())
                .potentiallyDuplicateIds(new ArrayList<>(validPotentiallyDuplicateIds))
                .payload(masterCatalogBatchJobMapper.toDto(job))
                .build();

        applicationEventPublisher.publish(BusinessApplicationEventType.GENERATE_PRODUCTS_SUBMIT, payload);
    }

    public void publishEventRemoveDuplicationCompleted(MasterCatalogBatchJob job,
            List<MasterCatalogPotentiallyDuplicateRawData> allDataBelongJob) {

        Set<UUID> uniqueData = allDataBelongJob.stream()
                .map(MasterCatalogPotentiallyDuplicateRawData::getPotentiallyDuplicateRawDataId)
                .collect(Collectors.toSet());

        Set<UUID> potentiallyNotDuplicate = allDataBelongJob.stream()
                .filter(data -> BooleanUtils.isNotTrue(data.getDuplicated()))
                .map(MasterCatalogPotentiallyDuplicateRawData::getRawDataId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        uniqueData.addAll(potentiallyNotDuplicate);
        log.info("REMOVE_DUPLICATION_SUBMIT listener, pushing {} data to calculate duplication for job {}",
                uniqueData.size(),
            job.getId());

        RemoveDuplicationCompletedPayload payload = RemoveDuplicationCompletedPayload.builder()
                .batchJobId(job.getId())
                .rawDataIds(new ArrayList<>(uniqueData))
                .payload(masterCatalogBatchJobMapper.toDto(job))
                .build();

        applicationEventPublisher.publish(BusinessApplicationEventType.REMOVE_DUPLICATION_COMPLETED, payload);
        log.info(
                "REMOVE_DUPLICATION_SUBMIT listener, published REMOVE_DUPLICATION_COMPLETED event for job {} with {} unique entries",
                job.getId(),
                uniqueData.size());
    }

    public void removeDuplicationInProgress(MasterCatalogBatchJob job,
            List<List<UUID>> potentiallyDuplicateList) {
        RemoveDuplicationInProgressPayload payload = RemoveDuplicationInProgressPayload.builder()
                .batchJobId(job.getId())
                .rawDataIds(potentiallyDuplicateList)
                .payload(masterCatalogBatchJobMapper.toDto(job))
                .build();
        applicationEventPublisher.publish(
                BusinessApplicationEventType.REMOVE_DUPLICATION_IN_PROGRESS, payload);
    }

    public void generateProductsInProgress(MasterCatalogBatchJob job,
            List<List<UUID>> potentiallyDuplicateList) {
        GenerateProductsInProgressPayload payload = GenerateProductsInProgressPayload.builder()
                .batchJobId(job.getId())
                .rawDataIds(potentiallyDuplicateList)
                .payload(masterCatalogBatchJobMapper.toDto(job))
                .build();
        applicationEventPublisher.publish(
                BusinessApplicationEventType.GENERATE_PRODUCTS_IN_PROGRESS, payload);
    }

    public void publishEventGenerateProductCompleted(MasterCatalogBatchJob job,
        List<MasterCatalogProduct> newProductList) {

        List<UUID> productIdList = newProductList.stream().map(MasterCatalogProduct::getId).toList();
        GenerateProductsCompletedPayload payload = GenerateProductsCompletedPayload.builder()
            .batchJobId(job.getId())
            .productIds(productIdList)
            .payload(masterCatalogBatchJobMapper.toDto(job))
            .build();

        applicationEventPublisher.publish(BusinessApplicationEventType.GENERATE_PRODUCTS_COMPLETED, payload);
    }

  public void publishMasterCatalogRawDataUpdatedEvent(UUID rawDataId,
      MasterCatalogRawData currentData, MasterCatalogRawData previousData) {
    log.info("Publishing Master Catalog Raw Data Updated event for rawDataId: {}", rawDataId);

    MasterCatalogRawDataUpdatedPayload payload = MasterCatalogRawDataUpdatedPayload.builder()
        .rawDataId(rawDataId)
        .currentData(currentData)
        .previousData(previousData)
        .build();

    applicationEventPublisher.publish(BusinessApplicationEventType.MASTER_CATALOG_RAW_DATA_UPDATED,
        payload);
  }

  public void publishMasterCatalogRawDataSyncedEvent(UUID rawDataId,
      MasterCatalogRawData currentData, MasterCatalogRawData previousData) {
    log.info("Publishing Master Catalog Raw Data Synced event for rawDataId: {}", rawDataId);

    MasterCatalogRawDataSyncedPayload payload = MasterCatalogRawDataSyncedPayload.builder()
        .rawDataId(rawDataId)
        .currentData(currentData)
        .previousData(previousData)
        .build();

    applicationEventPublisher.publish(BusinessApplicationEventType.MASTER_CATALOG_RAW_DATA_SYNCED,
        payload);
  }
}
