package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.enums.InventoryStates.IN_STOCK;
import static com.mercaso.data.master_catalog.enums.InventoryStates.SOLD;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryChangeRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSuqareInventoryDailyMetricRepository;
import com.mercaso.data.master_catalog.service.SuqareInventoryMetricsService;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RequiredArgsConstructor
@Service
public class SuqareInventoryMetricsServiceImpl implements SuqareInventoryMetricsService {

    private final MasterCatalogSquareInventoryRepository masterCatalogInventoryRepository;
    private final MasterCatalogSquareInventoryChangeRepository masterCatalogSquareInventoryChangeRepository;
    private final MasterCatalogSuqareInventoryDailyMetricRepository masterCatalogSuqareInventoryDailyMetricRepository;
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    private enum InventoryChangeReason {
        IN_STOCK,
        SOLD,
        UNKNOWN
    }

    @Transactional
    @Override
    public void initDailyMetrics(Integer daysBefore) {
        // Delete all existing daily metrics before initializing
        masterCatalogSuqareInventoryDailyMetricRepository.deleteAllInBatch();
        calculateDailyMetrics(daysBefore);
    }

    @Transactional
    @Override
    public void calculateYesterdayMetrics() {
        calculateDailyMetrics(1);
    }

    @Override
    @Transactional
    public void calculateDailyMetrics(Integer daysBefore) {

        Instant endDate = getLatestInventoryDate();
        Instant startDate = endDate.minus(daysBefore, ChronoUnit.DAYS);
        log.info("calculateDailyMetrics startDate: {}, endDate: {}", startDate, endDate);
        List<MasterCatalogSquareInventory> latestInventories = getLatestInventories();
        log.info("calculateDailyMetrics latestInventories size: {}", latestInventories.size());
        List<MasterCatalogSquareInventoryChange> changes = getInventoryChanges(startDate, endDate.truncatedTo(ChronoUnit.DAYS));
        Map<UUID, UUID> rawDataToStoreIdMap = findRawDataToStoreIdMap(latestInventories);
        log.info("calculateDailyMetrics rawDataToStoreIdMap: {}", rawDataToStoreIdMap);

        List<MasterCatalogSuqareInventoryDailyMetric> dailyMetrics = calculateAllDailyMetrics(latestInventories,
            rawDataToStoreIdMap,
            changes,
            startDate,
            endDate);

        saveDailyMetrics(dailyMetrics);
    }

    private Instant getLatestInventoryDate() {
        return masterCatalogInventoryRepository.findTopByOrderByUpdatedAtDesc()
            .map(MasterCatalogSquareInventory::getUpdatedAt)
            .orElseThrow(() -> new IllegalStateException("No inventory found"));
    }

    private List<MasterCatalogSquareInventory> getLatestInventories() {
        return masterCatalogInventoryRepository.findAllByStateOrderByUpdatedAtDesc(IN_STOCK.name());
    }

    private void saveDailyMetrics(List<MasterCatalogSuqareInventoryDailyMetric> dailyMetrics) {
        List<MasterCatalogSuqareInventoryDailyMetric> savedMetrics = masterCatalogSuqareInventoryDailyMetricRepository.saveAll(
            dailyMetrics);
        log.info("Saved {} daily metrics", savedMetrics.size());
    }

    private List<MasterCatalogSquareInventoryChange> getInventoryChanges(Instant startDate, Instant endDate) {
        return masterCatalogSquareInventoryChangeRepository.findAllByOccurredAtBetween(startDate, endDate);
    }

    private Map<UUID, UUID> findRawDataToStoreIdMap(List<MasterCatalogSquareInventory> latestInventories) {
        Set<UUID> rawDataIds = latestInventories.stream()
            .map(MasterCatalogSquareInventory::getMasterCatalogRawDataId)
            .collect(Collectors.toSet());
        List<MasterCatalogRawData> rawDataList = masterCatalogRawDataRepository.findAllById(rawDataIds);
        return rawDataList.stream()
            .collect(Collectors.toMap(MasterCatalogRawData::getId, MasterCatalogRawData::getStoreId));
    }

    private List<MasterCatalogSuqareInventoryDailyMetric> calculateAllDailyMetrics(
        List<MasterCatalogSquareInventory> latestInventories,
        Map<UUID, UUID> rawDataToStoreIdMap,
        List<MasterCatalogSquareInventoryChange> changes,
        Instant startDate,
        Instant endDate) {

        return latestInventories.stream()
            .map(inventory -> calculateDailyMetrics(inventory,
                rawDataToStoreIdMap.get(inventory.getMasterCatalogRawDataId()),
                changes,
                startDate,
                endDate))
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }

    private List<MasterCatalogSuqareInventoryDailyMetric> calculateDailyMetrics(
        MasterCatalogSquareInventory inventory,
        UUID storeId,
        List<MasterCatalogSquareInventoryChange> changes,
        Instant startDate,
        Instant endDate) {

        List<MasterCatalogSuqareInventoryDailyMetric> metrics = new ArrayList<>();
        Instant targetDate = endDate.minus(1, ChronoUnit.DAYS);

        while (!targetDate.isBefore(startDate)) {
            MasterCatalogSuqareInventoryDailyMetric initialMetric = createDailyMetric(inventory, storeId, targetDate);
            MasterCatalogSuqareInventoryDailyMetric finalMetric = updateMetricWithChanges(initialMetric,
                changes,
                inventory,
                targetDate);
            metrics.add(finalMetric);

            targetDate = targetDate.minus(1, ChronoUnit.DAYS);
        }

        return metrics;
    }

    private MasterCatalogSuqareInventoryDailyMetric createDailyMetric(MasterCatalogSquareInventory inventory, UUID storeId,
        Instant date) {
        MasterCatalogSuqareInventoryDailyMetric metric = new MasterCatalogSuqareInventoryDailyMetric();
        metric.setMasterCatalogRawDataId(inventory.getMasterCatalogRawDataId());
        metric.setStoreId(storeId);
        metric.setDate(date);
        return metric;
    }

    private MasterCatalogSuqareInventoryDailyMetric updateMetricWithChanges(
        MasterCatalogSuqareInventoryDailyMetric metric,
        List<MasterCatalogSquareInventoryChange> changes,
        MasterCatalogSquareInventory inventory,
        Instant currentDate) {

        UUID masterCatalogRawDataId = inventory.getMasterCatalogRawDataId();
        List<MasterCatalogSquareInventoryChange> dailyChanges = getDailyChanges(changes, masterCatalogRawDataId, currentDate);

        int[] quantities = calculateQuantities(dailyChanges, inventory.getQuantity());

        metric.setInStockQuantity(quantities[0]);
        metric.setOutStockQuantity(quantities[1]);
        metric.setQuantity(quantities[2]);
        return metric;
    }

    private List<MasterCatalogSquareInventoryChange> getDailyChanges(
        List<MasterCatalogSquareInventoryChange> changes,
        UUID masterCatalogRawDataId,
        Instant currentDate) {

        return changes.stream()
            .filter(change -> change.getOccurredAt()
                .truncatedTo(ChronoUnit.DAYS)
                .equals(currentDate.truncatedTo(ChronoUnit.DAYS)))
            .filter(change -> change.getMasterCatalogRawDataId().equals(masterCatalogRawDataId))
            .collect(Collectors.toList());
    }

    private int[] calculateQuantities(List<MasterCatalogSquareInventoryChange> dailyChanges, int initialQuantity) {
        int inStockQuantity = 0;
        int outStockQuantity = 0;
        int quantity = initialQuantity;

        for (MasterCatalogSquareInventoryChange change : dailyChanges) {
            InventoryChangeReason changeType = getInventoryChangeType(change.getToState());
            switch (changeType) {
                case SOLD:
                    quantity += change.getQuantity();
                    outStockQuantity += change.getQuantity();
                    break;
                case IN_STOCK:
                    quantity -= change.getQuantity();
                    inStockQuantity += change.getQuantity();
                    break;
                case UNKNOWN:
                    log.debug("Ignore the toState: {}", change.getToState());
                    break;
            }
        }

        return new int[]{inStockQuantity, outStockQuantity, quantity};
    }

    private InventoryChangeReason getInventoryChangeType(String toState) {
        if (SOLD.name().equals(toState)) {
            return InventoryChangeReason.SOLD;
        } else if (IN_STOCK.name().equals(toState)) {
            return InventoryChangeReason.IN_STOCK;
        } else {
            return InventoryChangeReason.UNKNOWN;
        }
    }
}
