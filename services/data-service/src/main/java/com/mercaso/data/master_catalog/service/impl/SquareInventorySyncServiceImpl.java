package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.DAYS_IN_ONE_YEAR;
import static com.mercaso.data.master_catalog.constants.SquareConstants.SQUARE_API_LIMIT;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryChangesRequestDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryCountsRequestDto;
import com.mercaso.data.master_catalog.dto.square.InventoryChangeDto;
import com.mercaso.data.master_catalog.dto.square.InventoryCountDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.enums.square.InventoryChangeTypes;
import com.mercaso.data.master_catalog.enums.square.SyncEntity;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryChangeRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.SquareInventorySyncService;
import com.mercaso.data.utils.SerializationUtils;
import com.squareup.square.models.InventoryAdjustment;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@RequiredArgsConstructor
@Slf4j
@Service
public class SquareInventorySyncServiceImpl implements SquareInventorySyncService {

    private final SquareApiAdapter squareApiAdapter;
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository;
    private final MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository;
    private final MasterCatalogSquareInventoryRepository masterCatalogSquareInventoryRepository;
    private final MasterCatalogSquareInventoryChangeRepository masterCatalogSquareInventoryChangeRepository;


    @Transactional
    @Override
    public void syncInventory(SquareDataSyncRequest request, List<String> presentAtLocationIds) {

        log.info("Synchronized inventory starting with request: {}", request);
        syncInventoryCounts(request, presentAtLocationIds);
        syncInventoryChanges(request, presentAtLocationIds);
        log.info("Synchronized inventory successfully with request: {}", request);
    }

    public void syncInventoryChanges(SquareDataSyncRequest request, List<String> presentAtLocationIds) {

        if (request.getSyncEntity() != null && request.getSyncEntity() != SyncEntity.INVENTORY_CHANGES) {
            log.info("Skipping inventory changes synchronization for request: {}", request);
            return;
        }

        UUID storeId = request.getStoreId();
        log.info("Synchronized inventory changes starting for store ID: {}", storeId);

        Instant latestSyncTime = request.getStartTimestamp() == null ? retrieveLatestSyncTimeForInventoryChanges(storeId)
            : Instant.ofEpochMilli(request.getStartTimestamp());

        List<InventoryChangeDto> inventoryChanges = fetchInventoryChanges(storeId, presentAtLocationIds, latestSyncTime);

        if (CollectionUtils.isEmpty(inventoryChanges)) {
            log.info("Synchronized inventory changes, no data found for store ID: {}", storeId);
            return;
        }

        UUID sourceId = saveRawInventoryChangesData(inventoryChanges);

        Map<String, UUID> variationIdToRawDataIdMap = findThenBuildVariationIdToRawDataIdMap(inventoryChanges, storeId);
        List<MasterCatalogSquareInventoryChange> inventoryChangeEntities = buildInventoryChangeEntities(
            inventoryChanges, variationIdToRawDataIdMap, sourceId);
        log.info("Synchronized inventory changes, built inventory change entities: {}", inventoryChangeEntities.size());
        saveInventoryChanges(inventoryChangeEntities);
        log.info("Synchronized inventory changes successfully for store ID: {}", storeId);
    }

    private UUID saveRawInventoryChangesData(List<InventoryChangeDto> inventoryCountList) {

        MasterCatalogSquareRawData savedMasterCatalogSquareRawData = masterCatalogSquareRawDataRepository.save(
            MasterCatalogSquareRawData.builder()
                .data(SerializationUtils.toTree(inventoryCountList))
                .build());
        return savedMasterCatalogSquareRawData.getId();
    }

    private List<InventoryChangeDto> fetchInventoryChanges(UUID storeId, List<String> presentAtLocationIds,
        Instant latestSyncTime) {
        BatchRetrieveInventoryChangesRequestDto request = BatchRetrieveInventoryChangesRequestDto.builder()
            .limit(SQUARE_API_LIMIT)
            .locationIds(presentAtLocationIds)
            .types(List.of(InventoryChangeTypes.ADJUSTMENT.name()))
            .updatedAfter(latestSyncTime.toString())
            .build();
        return squareApiAdapter.batchRetrieveInventoryChanges(storeId, request);
    }

    private Instant retrieveLatestSyncTimeForInventoryChanges(UUID storeId) {

        Optional<MasterCatalogSquareInventoryChange> inventoryChanges = masterCatalogSquareInventoryChangeRepository
            .findTopByStoreIdAndOrderByUpdatedAtDesc(storeId);
        return inventoryChanges.map(MasterCatalogSquareInventoryChange::getUpdatedAt)
            .orElseGet(() -> {
                log.info("Synchronized inventory changes, no sync record found for store ID: {}. Performing full synchronization.",
                    storeId);
                return Instant.now().minus(DAYS_IN_ONE_YEAR, ChronoUnit.DAYS);
            });
    }

    private void syncInventoryCounts(SquareDataSyncRequest request, List<String> presentAtLocationIds) {

        if (request.getSyncEntity() != null && request.getSyncEntity() != SyncEntity.INVENTORY) {
            log.info("Skipping inventory synchronization for request: {}", request);
            return;
        }

        UUID storeId = request.getStoreId();
        log.info("Synchronized inventory counts starting for store ID: {}", storeId);

        Instant latestSyncTime = request.getStartTimestamp() == null ? retrieveLatestSyncTimeForInventory(storeId)
            : Instant.ofEpochMilli(request.getStartTimestamp());

        List<InventoryCountDto> inventoryCountList = fetchInventoryCounts(storeId, presentAtLocationIds, latestSyncTime);

        if (CollectionUtils.isEmpty(inventoryCountList)) {
            log.info("Synchronized inventory, no inventory counts found for store ID: {}", storeId);
            return;
        }

        log.info("Synchronized inventory, found {} inventory counts.", inventoryCountList.size());

        UUID sourceId = saveRawInventoryData(inventoryCountList);

        List<MasterCatalogSquareInventory> masterCatalogSquareInventoryList = buildMasterCatalogSquareInventoryEntities(
            inventoryCountList, sourceId, storeId);
        log.info("Synchronized inventory, built inventory entities: {}", masterCatalogSquareInventoryList.size());

        saveInventory(masterCatalogSquareInventoryList);

        log.info("Synchronized inventory counts successfully for store ID: {}", storeId);
    }

    private Instant retrieveLatestSyncTimeForInventory(UUID storeId) {
        Optional<MasterCatalogSquareInventory> inventory = masterCatalogSquareInventoryRepository
            .findTopByStoreIdAndOrderByUpdatedAtDesc(storeId);
        return inventory.map(MasterCatalogSquareInventory::getUpdatedAt)
            .orElseGet(() -> {
                log.info("Synchronized inventory, no sync record found for store ID: {}. Performing full synchronization.",
                    storeId);
                return Instant.now().minus(DAYS_IN_ONE_YEAR, ChronoUnit.DAYS);
            });
    }

    private List<InventoryCountDto> fetchInventoryCounts(UUID storeId, List<String> presentAtLocationIds,
        Instant latestSyncTime) {

        BatchRetrieveInventoryCountsRequestDto requestDto = BatchRetrieveInventoryCountsRequestDto.builder()
            .limit(SQUARE_API_LIMIT)
            .locationIds(presentAtLocationIds)
            .updatedAfter(latestSyncTime.toString())
            .build();

        return squareApiAdapter.batchRetrieveInventoryCounts(storeId, requestDto);
    }

    private UUID saveRawInventoryData(List<InventoryCountDto> inventoryCountList) {
        MasterCatalogSquareRawData savedMasterCatalogSquareRawData = masterCatalogSquareRawDataRepository.save(
            MasterCatalogSquareRawData.builder()
                .data(SerializationUtils.toTree(inventoryCountList))
                .build());
        return savedMasterCatalogSquareRawData.getId();
    }

    private List<MasterCatalogSquareInventory> buildMasterCatalogSquareInventoryEntities(
        List<InventoryCountDto> inventoryCountList,
        UUID sourceId, UUID storeId) {

        // Map variationId to masterCatalogRawDataId
        Map<String, UUID> variationIdToRawDataIdMap = findAndBuildVariationIdToRawDataIdMap(inventoryCountList, storeId);
        log.info("Synchronized inventory, built variationId to masterCatalogRawDataId map: {}", variationIdToRawDataIdMap);

        // Map masterCatalogRawDataId to existing inventory
        Map<UUID, MasterCatalogSquareInventory> existingInventoryMap = findAndBuildExistingInventoryMap(variationIdToRawDataIdMap.values());
        log.info("Synchronized inventory, found {} existing inventory records.", existingInventoryMap.size());

        return inventoryCountList.stream()
            .map(inventoryCountDto -> buildCreateOrUpdateInventory(inventoryCountDto,
                sourceId,
                variationIdToRawDataIdMap,
                existingInventoryMap))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private Map<String, UUID> findAndBuildVariationIdToRawDataIdMap(List<InventoryCountDto> inventoryCountList, UUID storeId) {
        List<String> variationIds = inventoryCountList.stream()
            .map(InventoryCountDto::getCatalogObjectId)
            .collect(Collectors.toList());

        log.info("Synchronized inventory, found {} variation IDs to map, map {}.", variationIds.size(), variationIds);

        return variationMappingRepository.findAllByVariationIdInAndStoreIdIs(variationIds, storeId).stream()
            .collect(Collectors.toMap(
                MasterCatalogSquareVariationMapping::getVariationId,
                MasterCatalogSquareVariationMapping::getMasterCatalogRawDataId
            ));
    }

    private Map<UUID, MasterCatalogSquareInventory> findAndBuildExistingInventoryMap(Collection<UUID> masterCatalogRawDataIds) {
        return masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(new ArrayList<>(masterCatalogRawDataIds))
            .stream()
            .collect(Collectors.toMap(
                MasterCatalogSquareInventory::getMasterCatalogRawDataId,
                Function.identity()
            ));
    }

    private MasterCatalogSquareInventory buildCreateOrUpdateInventory(
        InventoryCountDto inventoryCountDto,
        UUID sourceId,
        Map<String, UUID> variationIdToRawDataIdMap,
        Map<UUID, MasterCatalogSquareInventory> existingInventoryMap) {

        String variationId = inventoryCountDto.getCatalogObjectId();
        UUID masterCatalogRawDataId = variationIdToRawDataIdMap.get(variationId);
        log.info("Synchronized inventory, found masterCatalogRawDataId: {} for variationId: {}",
            masterCatalogRawDataId,
            variationId);

        if (masterCatalogRawDataId == null) {
            log.info("Synchronized inventory, no mapping found for catalog object ID: {}", variationId);
            return null;
        }

        MasterCatalogSquareInventory updateSquareInventory = existingInventoryMap.get(masterCatalogRawDataId);

        if (updateSquareInventory != null) {

            updateSquareInventory.setQuantity(Integer.valueOf(inventoryCountDto.getQuantity()));
            updateSquareInventory.setState(inventoryCountDto.getState());
            updateSquareInventory.setSourceId(String.valueOf(sourceId));
            updateSquareInventory.setUpdatedAt(Instant.now());
            log.info("Synchronized inventory, build update inventory entity: {}", updateSquareInventory);
            return updateSquareInventory;

        } else {

            MasterCatalogSquareInventory newInventory = new MasterCatalogSquareInventory();
            newInventory.setId(UUID.randomUUID());
            newInventory.setMasterCatalogRawDataId(masterCatalogRawDataId);
            newInventory.setQuantity(Integer.valueOf(inventoryCountDto.getQuantity()));
            newInventory.setState(inventoryCountDto.getState());
            newInventory.setSourceId(String.valueOf(sourceId));
            newInventory.setUpdatedAt(Instant.now());
            log.info("Synchronized newInventory, built new inventory entity: {}", newInventory);
            return newInventory;
        }
    }

    private void saveInventory(List<MasterCatalogSquareInventory> inventoryList) {
        if (!inventoryList.isEmpty()) {
            masterCatalogSquareInventoryRepository.saveAll(inventoryList);
            log.info("Synchronized inventory, inserted {} inventory records.", inventoryList.size());
        }
    }

    private Map<String, UUID> findThenBuildVariationIdToRawDataIdMap(List<InventoryChangeDto> inventoryChangeDtoList,
        UUID storeId) {
        List<String> variationIds = inventoryChangeDtoList.stream()
            .filter(inventoryChangeDto -> inventoryChangeDto.getAdjustment() != null)
            .map(inventoryChangeDto -> inventoryChangeDto.getAdjustment().getCatalogObjectId())
            .collect(Collectors.toList());

        return variationMappingRepository.findAllByVariationIdInAndStoreIdIs(variationIds, storeId).stream()
            .collect(Collectors.toMap(
                MasterCatalogSquareVariationMapping::getVariationId,
                MasterCatalogSquareVariationMapping::getMasterCatalogRawDataId
            ));
    }

    private List<MasterCatalogSquareInventoryChange> buildInventoryChangeEntities(
        List<InventoryChangeDto> inventoryChanges,
        Map<String, UUID> variationIdToRawDataIdMap,
        UUID sourceId) {

        List<MasterCatalogSquareInventoryChange> inventoryChangeEntities = new ArrayList<>();

        for (InventoryChangeDto changeDto : inventoryChanges) {
            InventoryAdjustment adjustment = changeDto.getAdjustment();
            if (adjustment == null) {
                log.warn("Synchronized inventory changes, skipping inventory change due to missing adjustment information: {}",
                    changeDto);
                continue;
            }

            String variationId = adjustment.getCatalogObjectId();
            UUID rawDataId = variationIdToRawDataIdMap.get(variationId);

            if (rawDataId == null) {
                log.warn("Synchronized inventory changes, no raw data mapping found for variation ID: {}", variationId);
                continue;
            }

            MasterCatalogSquareInventoryChange inventoryChange = MasterCatalogSquareInventoryChange.builder()
                .id(UUID.randomUUID())
                .type(changeDto.getType())
                .masterCatalogRawDataId(rawDataId)
                .occurredAt(Instant.parse(adjustment.getOccurredAt()))
                .quantity(Integer.valueOf(adjustment.getQuantity()))
                .fromState(adjustment.getFromState())
                .toState(adjustment.getToState())
                .sourceId(sourceId.toString())
                .updatedAt(Instant.now())
                .build();

            inventoryChangeEntities.add(inventoryChange);
            log.debug("Synchronized inventory changes, built inventory change entity: {}", inventoryChange);
        }

        log.debug("Synchronized inventory changes, total inventory change entities built: {}", inventoryChangeEntities.size());
        return inventoryChangeEntities;
    }

    private void saveInventoryChanges(List<MasterCatalogSquareInventoryChange> inventoryChanges) {
        if (CollectionUtils.isEmpty(inventoryChanges)) {
            log.info("Synchronized inventory changes, no valid inventory changes to save.");
            return;
        }

        masterCatalogSquareInventoryChangeRepository.saveAll(inventoryChanges);
        log.info("Synchronized inventory changes , inserted {} inventory change records.", inventoryChanges.size());
    }

}
