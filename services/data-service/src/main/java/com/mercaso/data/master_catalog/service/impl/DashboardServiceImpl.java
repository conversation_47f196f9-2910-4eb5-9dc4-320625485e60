package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.dto.dashboard.DashboardRequest;
import com.mercaso.data.master_catalog.dto.dashboard.InventoryAndReplenishmentTrendDto;
import com.mercaso.data.master_catalog.dto.dashboard.OrderTrendDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSuqareInventoryDailyMetricRepository;
import com.mercaso.data.master_catalog.service.DashboardService;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final MasterCatalogSuqareInventoryDailyMetricRepository inventoryMetricRepository;
    private final MasterCatalogSquareOrderRepository orderRepository;
    private final MasterCatalogRawDataRepository rawDataRepository;
    private final MasterCatalogSquareOrderLineItemRepository orderLineItemRepository;

    @Override
    public List<InventoryAndReplenishmentTrendDto> getInventoryReplenishmentTrend(DashboardRequest request) {
        UUID rawDataId = findRawDataId(request);
        if (rawDataId == null) {
            log.warn("Raw data ID not found, storeId: {}, UPC: {}", request.getStoreId(), request.getUpc());
            return Collections.emptyList();
        }

        DateRange dateRange = getDateRange(request.getDaysBefore());
        List<MasterCatalogSuqareInventoryDailyMetric> metrics = inventoryMetricRepository.findByDateRangeAndNotBothQuantitiesZero(
            rawDataId,
            dateRange.startDate(),
            dateRange.endDate());

        if (metrics.isEmpty()) {
            log.info("No valid inventory metrics found, rawDataId: {}", rawDataId);
            return Collections.emptyList();
        }

        Map<Instant, InventoryAndReplenishmentTrendDto> inventoryMap = createInventoryMap(metrics);
        return createTrendDtoList(dateRange, inventoryMap, this::createInventoryDto);
    }

    @Override
    public List<OrderTrendDto> getOrderTrend(DashboardRequest request) {
        UUID rawDataId = findRawDataId(request);
        if (rawDataId == null) {
            log.warn("Raw data ID not found, storeId: {}, UPC: {}", request.getStoreId(), request.getUpc());
            return Collections.emptyList();
        }

        List<UUID> orderIds = orderLineItemRepository.findOrderIdsByMasterCatalogRawDataId(rawDataId);
        if (orderIds.isEmpty()) {
            log.info("No orders found, rawDataId: {}", rawDataId);
            return Collections.emptyList();
        }

        DateRange dateRange = getDateRange(request.getDaysBefore());
        List<MasterCatalogSquareOrder> orders = orderRepository.findByIdInAndCreatedAtBetweenOrderByCreatedAtDesc(
            orderIds, dateRange.startDate(), dateRange.endDate());

        Map<Instant, Long> orderCounts = createOrderCountMap(orders);
        return createTrendDtoList(dateRange, orderCounts, this::createOrderDto);
    }

    private UUID findRawDataId(DashboardRequest request) {
        List<UUID> rawDataIds = rawDataRepository.findIdsByStoreIdAndUpc(request.getStoreId(), request.getUpc());
        if (rawDataIds.isEmpty()) {
            return null;
        }
        if (rawDataIds.size() > 1) {
            log.warn("Multiple MasterCatalogRawData IDs found, storeId: {}, UPC: {}. IDs: {}. Using the first ID: {}",
                request.getStoreId(), request.getUpc(), rawDataIds, rawDataIds.getFirst());
        }
        return rawDataIds.getFirst();
    }

    private DateRange getDateRange(Integer daysBefore) {
        int days = Optional.ofNullable(daysBefore).filter(d -> d > 0).orElse(30);
        Instant endDate = Instant.now().truncatedTo(ChronoUnit.DAYS);
        Instant startDate = endDate.minus(days, ChronoUnit.DAYS);
        return new DateRange(startDate, endDate);
    }

    private Map<Instant, InventoryAndReplenishmentTrendDto> createInventoryMap(
        List<MasterCatalogSuqareInventoryDailyMetric> metrics) {
        return metrics.stream()
            .collect(Collectors.groupingBy(
                metric -> metric.getDate().truncatedTo(ChronoUnit.DAYS),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        MasterCatalogSuqareInventoryDailyMetric firstMetric = list.getFirst();
                        return InventoryAndReplenishmentTrendDto.builder()
                            .date(firstMetric.getDate().truncatedTo(ChronoUnit.DAYS))
                            .quantity(list.stream().mapToInt(MasterCatalogSuqareInventoryDailyMetric::getQuantity).sum())
                            .inStockQuantity(list.stream()
                                .mapToInt(MasterCatalogSuqareInventoryDailyMetric::getInStockQuantity)
                                .sum())
                            .build();
                    }
                )
            ));
    }

    private Map<Instant, Long> createOrderCountMap(List<MasterCatalogSquareOrder> orders) {
        return orders.stream()
            .collect(Collectors.groupingBy(
                order -> order.getOrderCreatedAt().truncatedTo(ChronoUnit.DAYS),
                Collectors.counting()
            ));
    }

    private <T> List<T> createTrendDtoList(DateRange dateRange, Map<Instant, ?> dataMap, DtoCreator<T> dtoCreator) {
        return Stream.iterate(dateRange.startDate(),
                date -> date.isBefore(dateRange.endDate()),
                date -> date.plus(1, ChronoUnit.DAYS))
            .map(date -> dtoCreator.create(date, dataMap.get(date)))
            .sorted(Comparator.comparing(this::getDate).reversed())
            .collect(Collectors.toList());
    }

    private InventoryAndReplenishmentTrendDto createInventoryDto(Instant date, Object data) {
        return Optional.ofNullable(data)
            .filter(InventoryAndReplenishmentTrendDto.class::isInstance)
            .map(InventoryAndReplenishmentTrendDto.class::cast)
            .orElseGet(() -> InventoryAndReplenishmentTrendDto.builder()
                .date(date)
                .quantity(0)
                .inStockQuantity(0)
                .build());
    }

    private OrderTrendDto createOrderDto(Instant date, Object data) {
        int quantity = Optional.ofNullable(data)
            .filter(Long.class::isInstance)
            .map(Long.class::cast)
            .map(Long::intValue)
            .orElse(0);
        return OrderTrendDto.builder()
            .date(date)
            .quantity(quantity)
            .build();
    }

    private Instant getDate(Object dto) {
        if (dto instanceof InventoryAndReplenishmentTrendDto) {
            return ((InventoryAndReplenishmentTrendDto) dto).getDate();
        } else if (dto instanceof OrderTrendDto) {
            return ((OrderTrendDto) dto).getDate();
        }
        throw new IllegalArgumentException("Unsupported DTO type: " + dto.getClass().getName());
    }

    private record DateRange(Instant startDate, Instant endDate) {

    }

    @FunctionalInterface
    private interface DtoCreator<T> {

        T create(Instant date, Object data);
    }
}
