package com.mercaso.data.image_management.mapper;

import com.mercaso.data.image_management.dto.ImageManagementItemImageDto;
import com.mercaso.data.image_management.entity.ImageManagementItemImage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface ImageManagementItemImageMapper{
    
    @Mapping(source = "eachFlag", target = "isEach")
    ImageManagementItemImageDto toDto(ImageManagementItemImage entity);

} 