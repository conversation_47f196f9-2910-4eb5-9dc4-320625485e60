package com.mercaso.data.master_catalog.dto;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation}
 */
@AllArgsConstructor
@Getter
public class MasterCatalogProductAssociationDto extends BaseDto {

    private final UUID id;
    private final Instant createdAt;
    private final Instant updatedAt;
    private final String upc;
    private final UUID associationGroup;
}
