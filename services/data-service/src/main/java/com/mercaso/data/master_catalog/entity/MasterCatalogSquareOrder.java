package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "master_catalog_square_order")
public class MasterCatalogSquareOrder extends BaseEntity {

    @Size(max = 255)
    @Column(name = "state")
    private String state;

    @Column(name = "store_id")
    private UUID storeId;

    @Size(max = 255)
    @Column(name = "source_id")
    private String sourceId;

    @Column(name = "order_closed_at")
    private Instant orderClosedAt;

    @Column(name = "order_created_at")
    private Instant orderCreatedAt;

    @Column(name = "order_updated_at")
    private Instant orderUpdatedAt;

}
