package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.constants.CommonConstants;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyAssociateProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogPotentiallyAssociateProductMapper extends
    BaseDoMapper<MasterCatalogPotentiallyAssociateProductDto, MasterCatalogPotentiallyAssociateProduct>{

  @Mapping(source = "jobId", target = "jobId")
  @Mapping(source = "taskId", target = "taskId")
  @Mapping(source = "product.id", target = "productId")
  @Mapping(source = "product.name", target = "productName")
  @Mapping(source = "product.upc", target = "productUpc")
  @Mapping(source = "potentialAssociateProduct.id", target = "potentiallyAssociateProductId")
  @Mapping(source = "potentialAssociateProduct.name", target = "potentiallyAssociateProductName")
  @Mapping(source = "potentialAssociateProduct.upc", target = "potentiallyAssociateProductUpc")
  @Mapping(target = "status", source = "status")
  @Mapping(target = "id", source = "id")
  @Mapping(target = "createdBy", constant = CommonConstants.SYSTEM_USER_ID)
  @Mapping(target = "updatedBy", constant = CommonConstants.SYSTEM_USER_ID)
  @Mapping(target = "createdAt", expression = "java(java.time.Instant.now())")
  @Mapping(target = "updatedAt", expression = "java(java.time.Instant.now())")
  @Mapping(target = "associated", ignore = true)
  MasterCatalogPotentiallyAssociateProduct mapProductToPotentialAssociateProduct(
      UUID id,
      UUID jobId,
      UUID taskId,
      MasterCatalogProduct product,
      MasterCatalogProduct potentialAssociateProduct,
      PotentiallyAssociateProductStatus status);
}
