package com.mercaso.data.master_catalog.event.model;

import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Setter
@Getter
public abstract class BaseApplicationEvent<T extends BusinessEventPayload<?>> extends ApplicationEvent {

    private T payload;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with which the event is associated (never
     *     {@code null})
     */
    protected BaseApplicationEvent(Object source, T payload) {
        super(source);
        this.payload = payload;
    }
}
