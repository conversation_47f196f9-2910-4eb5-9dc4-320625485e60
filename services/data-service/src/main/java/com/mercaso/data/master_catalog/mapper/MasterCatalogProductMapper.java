package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogProductMapper extends
    BaseDoMapper<MasterCatalogProductDto, MasterCatalogProduct> {

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    MasterCatalogProduct partialUpdate(
        MasterCatalogProductDto masterCatalogProductDto, @MappingTarget MasterCatalogProduct masterCatalogProduct);
}
