package com.mercaso.data.master_catalog.event.factory;

import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.BusinessEventEntityDto;
import com.mercaso.data.master_catalog.event.annotation.BusinessEntityIdentifier;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import com.mercaso.data.utils.SerializationUtils;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Business event DTO factory, responsible for creating BusinessEventDto
 */
@Component
@NoArgsConstructor
public class BusinessEventDtoFactory {

    /**
     * Create business event DTO
     */
    public BusinessEventDto createBusinessEventDto(
        String eventType,
        BusinessEventPayload<?> payload) {

        return BusinessEventDto.builder()
            .id(UUID.randomUUID().toString())
            .type(eventType)
            .createdAt(Instant.now())
            .payload(SerializationUtils.toTree(payload))
            .entities(buildEntities(payload))
            .build();
    }

    /**
     * Build event related entity list
     */
    private List<BusinessEventEntityDto> buildEntities(BusinessEventPayload<?> payload) {
        List<BusinessEventEntityDto> entities = new ArrayList<>();

        for (Field field : payload.getClass().getDeclaredFields()) {
            BusinessEntityIdentifier annotation = field.getAnnotation(BusinessEntityIdentifier.class);
            if (annotation != null) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(payload);
                    if (value == null) {
                        if (annotation.required()) {
                            throw new IllegalArgumentException(
                                "Required entity identifier field is null: " + field.getName());
                        }
                        continue;
                    }

                    entities.add(BusinessEventEntityDto.builder()
                        .entityType(annotation.value())
                        .entityId(value.toString())
                        .build());

                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Failed to access field: " + field.getName(), e);
                }
            }
        }

        return entities;
    }
} 