package com.mercaso.data.master_catalog.dto;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogSquareInventoryChangeDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private UUID masterCatalogRawDataId;
    private String type;
    private String fromState;
    private String toState;
    private Long quantity;
    private Instant occurredAt;
}