package com.mercaso.data.image_management.controller;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.image_management.dto.ImageManagementItemImageDto;
import com.mercaso.data.image_management.dto.ImageSearchRequestDto;
import com.mercaso.data.image_management.dto.Result;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.image_management.service.ImageManagementImageService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/v1/image-management/image")
@RequiredArgsConstructor
@Slf4j
public class ImageManagementImageResource {

  private final ImageManagementImageService imageManageService;

  @PostMapping("/upload")
  @PreAuthorize("hasAnyAuthority('image-management:write:image')")
  public Result<Void> uploadImage(@RequestParam("shotAt") String shotAt,
      @RequestParam("fileName") String fileName,
      @RequestParam("imageType") ImageTypeEnum imageType,
      @RequestParam("file") MultipartFile file) {
    String code = imageManageService.uploadAndSave(shotAt, fileName, imageType, file);
    return Result.success(code);
  }

  @GetMapping("/search")
  @PreAuthorize("hasAnyAuthority('image-management:read:image')")
  public ResponseEntity<CustomPage<ImageManagementItemImageDto>> searchImages(
      @ModelAttribute @Valid ImageSearchRequestDto request) {
    Page<ImageManagementItemImageDto> result = imageManageService.searchImages(request);
    return ResponseEntity.ok(new CustomPage<ImageManagementItemImageDto>().build(result));
  }
}
