package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.exception.SquareAuthorizationNotFoundException;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import com.mercaso.data.master_catalog.service.SquareClientService;
import com.squareup.square.Environment;
import com.squareup.square.SquareClient;
import com.squareup.square.SquareClient.Builder;
import com.squareup.square.authentication.BearerAuthModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class SquareClientServiceImpl implements SquareClientService {

    private final MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository;
    private final CipherUtility cipherUtility;
    
    private static final Environment DEFAULT_ENVIRONMENT = Environment.PRODUCTION;
    private static final String STORE_ID_NULL_MESSAGE = "StoreId cannot be null";
    private static final String AUTH_NOT_FOUND_MESSAGE = "Square authorization not found for storeId: %s";

    /**
     * Retrieves a Square client instance for a specific store.
     *
     * @param storeId The unique identifier of the store
     * @return A configured SquareClient instance with the store's authorization
     * @throws NullPointerException if storeId is null
     * @throws SquareAuthorizationNotFoundException if no Square authorization is found for the store
     */
    @Override
    public SquareClient getSquareClient(UUID storeId) {
        Objects.requireNonNull(storeId, STORE_ID_NULL_MESSAGE);
        
        MasterCatalogSquareAuthorization squareAuthorization = masterCatalogSquareAuthorizationRepository
            .findByStoreId(storeId);

        if (squareAuthorization == null) {
            String errorMessage = String.format(AUTH_NOT_FOUND_MESSAGE, storeId);
            log.error(errorMessage);
            throw new SquareAuthorizationNotFoundException(errorMessage);
        }

        return buildSquareClient(cipherUtility.decrypt(squareAuthorization.getEncryptedAccessToken()));
    }

    /**
     * Creates a default Square client instance without authentication.
     *
     * @return A configured SquareClient instance without authorization credentials
     */
    @Override
    public SquareClient getDefaultSquareClient() {
        return buildSquareClient(null);
    }
    
    /**
     * Builds a Square client with the specified access token.
     *
     * @param accessToken The access token for Square API authentication, can be null for unauthorized clients
     * @return A configured SquareClient instance
     */
    private SquareClient buildSquareClient(String accessToken) {
        Builder builder = new Builder();
        builder.environment(DEFAULT_ENVIRONMENT);

        if (StringUtils.isNotBlank(accessToken)) {
            builder.bearerAuthCredentials(new BearerAuthModel.Builder(accessToken).build());
        }

        return builder.build();
    }
}
