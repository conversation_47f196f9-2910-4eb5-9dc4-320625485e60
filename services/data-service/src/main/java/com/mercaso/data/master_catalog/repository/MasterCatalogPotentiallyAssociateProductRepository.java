package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyAssociateProduct;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogPotentiallyAssociateProductRepository extends
    JpaRepository<MasterCatalogPotentiallyAssociateProduct, UUID>, JpaSpecificationExecutor<MasterCatalogPotentiallyAssociateProduct> {

  List<MasterCatalogPotentiallyAssociateProduct> findByTaskIdIn(List<UUID> taskIds);

  List<MasterCatalogPotentiallyAssociateProduct> findByJobId(UUID jobId);

  List<MasterCatalogPotentiallyAssociateProduct> findByTaskId(UUID taskId);
}