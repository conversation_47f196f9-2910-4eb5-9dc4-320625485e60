package com.mercaso.data.master_catalog.entity;

import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "mc_potentially_associate_product")
public class MasterCatalogPotentiallyAssociateProduct extends BaseEntity {

  @Column(name = "product_id")
  private UUID productId;

  @Size(max = 255)
  @Column(name = "product_name")
  private String productName;

  @Size(max = 64)
  @Column(name = "product_upc")
  private String productUpc;

  @Column(name = "potentially_associate_product_id")
  private UUID potentiallyAssociateProductId;

  @Size(max = 255)
  @Column(name = "potentially_associate_product_name")
  private String potentiallyAssociateProductName;

  @Size(max = 64)
  @Column(name = "potentially_associate_product_upc")
  private String potentiallyAssociateProductUpc;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private PotentiallyAssociateProductStatus status;

  @Column(name = "task_id", nullable = false)
  private UUID taskId;

  @Column(name = "job_id", nullable = false)
  private UUID jobId;

  @Column(name = "associated")
  private Boolean associated;

  @Size(max = 64)
  @Column(name = "created_by", nullable = false)
  private String createdBy;

  @Size(max = 64)
  @Column(name = "updated_by", nullable = false)
  private String updatedBy;
}
