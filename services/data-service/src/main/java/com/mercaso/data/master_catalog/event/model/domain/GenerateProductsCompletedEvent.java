package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsCompletedPayload;

@EventPublishConfig(
    publishLocal = true
)
public class GenerateProductsCompletedEvent extends BaseApplicationEvent<GenerateProductsCompletedPayload> {

  public GenerateProductsCompletedEvent(Object source, GenerateProductsCompletedPayload payload) {
    super(source, payload);
  }
}
