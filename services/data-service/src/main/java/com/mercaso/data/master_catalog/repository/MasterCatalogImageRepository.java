package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MasterCatalogImageRepository extends JpaRepository<MasterCatalogImage, UUID> {

  List<MasterCatalogImage> findAllByMasterCatalogRawDataIdIn(List<UUID> masterCatalogRawDataIds);

  List<MasterCatalogImage> findByMasterCatalogRawDataId(UUID masterCatalogRawDataId);

  void deleteByMasterCatalogRawDataId(UUID masterCatalogRawDataId);
}
