package com.mercaso.data.master_catalog.event.applicationevent.api;


import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;

/**
 * Event type interface, defines the basic characteristics of event types
 * Replaces the original enum implementation, providing a more flexible way to define event types
 */
public interface ApplicationEventType<P extends BusinessEventPayload<?>, E extends BaseApplicationEvent<P>> {

    /**
     * Get the unique name of the event type
     */
    String getName();

    /**
     * Get payload type Class
     */
    Class<P> getPayloadClass();

    /**
     * Get event type Class
     */
    Class<E> getApplicationEventClass();

    /**
     * Create event instance
     */
    E createEvent(Object source, Object payload);

    boolean shouldPersist();

    boolean shouldPublishLocal();
} 