package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.dto.StoreDto;
import com.mercaso.data.master_catalog.service.StoreService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("master-catalog/v1/stores")
public class StoresResource {

    private final StoreService storeService;

    @PreAuthorize("hasAnyAuthority('master-catalog:read:stores')")
    @GetMapping
    public ResponseEntity<List<StoreDto>> getStores() {
        return ResponseEntity.ok(storeService.getStores());
    }
}
