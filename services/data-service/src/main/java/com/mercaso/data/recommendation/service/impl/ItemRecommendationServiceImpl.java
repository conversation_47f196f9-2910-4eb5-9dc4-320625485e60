package com.mercaso.data.recommendation.service.impl;

import com.mercaso.data.recommendation.ItemRecommendationService;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import com.mercaso.data.recommendation.mapper.ItemRecommendationMapper;
import com.mercaso.data.recommendation.repository.ItemRecommendationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemRecommendationServiceImpl implements ItemRecommendationService {

  private final ItemRecommendationRepository itemRecommendationRepository;
  private final ItemRecommendationMapper itemRecommendationMapper;

  @Override
  public PageableResponse<ItemRecommendationDto> search(String storeId, String version,
      String departmentId, List<String> reasons, Integer pageNumber,
      Integer pageSize) {
    Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC,
        "reasonValue"));

    Specification<ItemRecommendation> spec = buildSearchSpecification(storeId, version, departmentId, reasons);
    Page<ItemRecommendation> recommendations = itemRecommendationRepository.findAll(spec, pageable);

    log.info("Search item recommendations for store: {}, version: {}, departmentId: {}, reasons: {}, page: {}, size: {}",
        storeId, version, departmentId, reasons, pageNumber, pageSize);

    List<ItemRecommendationDto> dtos = recommendations.getContent().stream()
        .map(itemRecommendationMapper::toDto)
        .toList();

    return PageableResponse.<ItemRecommendationDto>builder()
        .data(dtos)
        .pageNumber(recommendations.getNumber())
        .pageSize(recommendations.getSize())
        .totalPages(recommendations.getTotalPages())
        .totalElements((int) recommendations.getTotalElements())
        .build();
  }

  private Specification<ItemRecommendation> buildSearchSpecification(String storeId, String version, 
      String departmentId, List<String> reasons) {
    return (root, query, criteriaBuilder) -> {
      List<Predicate> predicates = new ArrayList<>();
      
      predicates.add(criteriaBuilder.equal(root.get("storeId"), storeId));
      predicates.add(criteriaBuilder.equal(root.get("version"), version));
      
      // For legacy versions (V1, V2), ignore departmentId filter
      if (!isLegacyVersion(version) && StringUtils.hasText(departmentId)) {
        predicates.add(criteriaBuilder.equal(root.get("departmentId"), departmentId));
      }
      
      if (reasons != null && !reasons.isEmpty()) {
        predicates.add(root.get("reason").in(reasons));
      }
      
      return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
    };
  }

  private boolean isLegacyVersion(String version) {
    return "V1".equals(version) || "V2".equals(version);
  }

  @Override
  public List<DepartmentDto> getDepartments(String storeId, String version) {
    log.info("Getting distinct departments for store: {}, version: {}", storeId, version);

    List<Object[]> departmentData = itemRecommendationRepository
        .findDistinctDepartmentsWithIdByStoreIdAndVersion(storeId, version);

    List<DepartmentDto> departments = departmentData.stream()
        .map(data -> new DepartmentDto((String) data[0], (String) data[1])) // departmentId, department
        .toList();

    log.info("Found {} distinct departments for store: {}, version: {}", departments.size(),
        storeId, version);

    return departments;
  }
}
