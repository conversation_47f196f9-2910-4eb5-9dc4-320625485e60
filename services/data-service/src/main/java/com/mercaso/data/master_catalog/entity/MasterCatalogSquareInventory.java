package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_square_inventory")
public class MasterCatalogSquareInventory extends BaseEntity {

    @Size(max = 255)
    @Column(name = "state")
    private String state;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "master_catalog_raw_data_id")
    private UUID masterCatalogRawDataId;

    @Size(max = 255)
    @Column(name = "source_id")
    private String sourceId;

}
