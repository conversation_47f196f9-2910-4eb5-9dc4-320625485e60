package com.mercaso.data.recommendation;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import java.util.List;

public interface ItemRecommendationService {

  PageableResponse<ItemRecommendationDto> search(String storeId, String version, String departmentId, List<String> reasons, Integer pageNumber,
      Integer pageSize);

  List<DepartmentDto> getDepartments(String storeId, String version);
}
