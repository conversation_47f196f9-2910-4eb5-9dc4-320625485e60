package com.mercaso.data.master_catalog.event.applicationevent.api;

import com.mercaso.data.master_catalog.event.applicationevent.BusinessApplicationEventType;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.GenericTypeResolver;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * Application event listener interface, providing common structure and error handling for all event listeners.
 *
 * @param <E> The type of event this listener handles, must extend BaseApplicationEvent
 * @param <P> The type of payload this event contains, must extend BusinessEventPayload
 */
public interface ApplicationEventListener<E extends BaseApplicationEvent<P>, P extends BusinessEventPayload<?>> {

    // Define static Logger for all default methods
    Logger log = LoggerFactory.getLogger(ApplicationEventListener.class);

    /**
     * Get the event type
     *
     * @return Class object of the event type
     */
    @SuppressWarnings("unchecked")
    default Class<E> getEventType() {
        // Obtain generic parameter types through reflection
        Class<?>[] genericTypes = GenericTypeResolver.resolveTypeArguments(getClass(), ApplicationEventListener.class);
        if (genericTypes != null && genericTypes.length > 0) {
            return (Class<E>) genericTypes[0];
        } else {
            throw new IllegalStateException("Could not determine event type for listener: " + getClass().getSimpleName());
        }
    }

    /**
     * Initialization method, find the corresponding BusinessEventType
     */
    @PostConstruct
    default void init() {
        Class<E> eventType = getEventType();
        BusinessApplicationEventType businessEventType = null;

        // Find corresponding BusinessEventType
        for (BusinessApplicationEventType type : BusinessApplicationEventType.values()) {
            if (type.getApplicationEventClass().equals(eventType)) {
                businessEventType = type;
                break;
            }
        }

        log.info("Listener {} initialized for event type: {} ({})",
            getClass().getSimpleName(),
            eventType.getSimpleName(),
            businessEventType != null ? businessEventType.name() : "Unknown business event type");
    }

    /**
     * A general event listening method that automatically filters out the event types of interest to the current listener.
     *
     * @param event Received events
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    default void onApplicationEvent(BaseApplicationEvent<?> event) {
        Class<E> eventType = getEventType();
        if (eventType.isInstance(event)) {
            @SuppressWarnings("unchecked")
            E typedEvent = (E) event;
            processEvent(typedEvent);
        }
    }

    /**
     * Standard implementation of event processing, including error handling
     *
     * @param event Events to be processed
     */
    default void processEvent(E event) {
        String eventType = event.getClass().getSimpleName();
        log.info("Processing event: {}", eventType);

        try {
            handleEvent(event);
            log.info("Successfully processed event: {}", eventType);
        } catch (Exception exception) {
            log.error("Failed to process event {}: {}", eventType, exception.getMessage(), exception);
            handleEventProcessingFailure(event, exception);
        }
    }

    /**
     * The specific implementation of handling events is provided by subclasses.
     *
     * @param event Events to be processed
     */
    void handleEvent(E event);

    /**
     * Handling the case where event processing fails, the default implementation is to rethrow the exception.
     *
     * @param event Failed Event Handling
     * @param exception The occurred exception
     */
    default void handleEventProcessingFailure(E event, Exception exception) {
        throw new RuntimeException("Event processing failed for " + event.getClass().getSimpleName(), exception);
    }

    /**
     * Get the business event type associated with this listener.
     *
     * @return The business event type
     */
    default BusinessApplicationEventType getBusinessEventType() {
        Class<E> eventType = getEventType();
        for (BusinessApplicationEventType type : BusinessApplicationEventType.values()) {
            if (type.getApplicationEventClass().equals(eventType)) {
                return type;
            }
        }
        return null;
    }
} 