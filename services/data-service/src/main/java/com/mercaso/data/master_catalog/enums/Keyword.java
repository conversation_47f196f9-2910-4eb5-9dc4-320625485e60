package com.mercaso.data.master_catalog.enums;

public enum Keyword {
    UPC,
    DESCRIPTION,
    STORE_ID,
    ;

    public static String nameFromValue(String value) {
        if (value == null) {
            return null;
        }
        for (Keyword keyword : Keyword.values()) {
            if (value.equals(keyword.name())) {
                return keyword.name();
            }
        }
        return null;
    }
}
