package com.mercaso.data.image_management.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {

  private String code;
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private T data;

  public static <T> Result<T> success(String code) {
    return new Result<>(code, null);
  }
  public static <T> Result<T> success(String code, T data) {
    return new Result<>(code, data);
  }

  public static <T> Result<T> error(String code) {
    return new Result<>(code, null);
  }
}
