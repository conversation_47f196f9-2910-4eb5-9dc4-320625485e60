package com.mercaso.data.master_catalog.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.service.SquareDataSyncService;
import com.mercaso.data.master_catalog.service.SquareInventorySyncService;
import com.mercaso.data.master_catalog.service.SquareItemSyncService;
import com.mercaso.data.master_catalog.service.SquareOrderSyncService;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class SquareDataSyncServiceImpl implements SquareDataSyncService {

    private final StoreRepository storeRepository;
    private final SquareInventorySyncService squareInventorySyncService;
    private final SquareItemSyncService squareItemSyncService;
    private final SquareOrderSyncService squareOrderSyncService;
    private final MasterCatalogLocationRepository masterCatalogLocationRepository;
    private final MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository;

    @Override
    public void syncSquareData(SquareDataSyncRequest request) {

        if (request != null && request.getStoreId() != null) {

            log.info("Synchronized square data for store ID: {}", request.getStoreId());
            synchronizeStoreData(request);
            return;
        }

        log.info("Synchronizing square data for all stores");
        List<Store> squareStores = storeRepository.findAllByIntegrationTypeIs("SQUARE");

        if (CollectionUtils.isEmpty(squareStores)) {
            log.info("Synchronized square data, no stores found.");
            return;
        }

        for (Store squareStore : squareStores) {
            request = new SquareDataSyncRequest();
            request.setStoreId(squareStore.getId());
            synchronizeStoreData(request);
        }
    }

    private void synchronizeStoreData(SquareDataSyncRequest request) {
        UUID storeId = request.getStoreId();

        MasterCatalogSquareAuthorization authorization = masterCatalogSquareAuthorizationRepository.findByStoreId(storeId);

        if (authorization == null) {
            log.error("Square authorization not found for storeId: {}", storeId);
            return;
        }

        // Sync items
        squareItemSyncService.syncItems(request);

        List<String> locationIds = this.findLocationIds(storeId);

        if (CollectionUtils.isEmpty(locationIds)) {
            log.warn("Synchronized square data, no locations found for store: {}", storeId);
            return;
        }

        // Sync inventory
        squareInventorySyncService.syncInventory(request, locationIds);
        // Sync Order
        squareOrderSyncService.syncOrder(request, locationIds);
    }


    private List<String> findLocationIds(UUID storeId) {
        List<MasterCatalogLocation> allLocations = masterCatalogLocationRepository.findAllByStoreId(storeId);
        if (CollectionUtils.isEmpty(allLocations)) {
            return null;
        }
        return allLocations.stream().map(l -> {
            JsonNode locationId = l.getMetadata().path("locationId");
            return locationId.isMissingNode() ? null : locationId.asText();
        }).distinct().collect(Collectors.toList());
    }
}
