package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.service.CipherUtility;
import com.mercaso.data.master_catalog.service.impl.CipherUtilityImpl;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogSquareAuthorizationMapper extends
  BaseDoMapper<MasterCatalogSquareAuthorizationDto, MasterCatalogSquareAuthorization> {

}
