package com.mercaso.data.image_management.service.impl;

import static com.mercaso.data.master_catalog.exception.ErrorCodeEnums.UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE;

import com.mercaso.data.image_management.dto.ImageManagementItemImageDto;
import com.mercaso.data.image_management.dto.ImageSearchRequestDto;
import com.mercaso.data.image_management.entity.ImageManagementImage;
import com.mercaso.data.image_management.entity.ImageManagementItemImage;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.image_management.exception.ImageManagementImageException;
import com.mercaso.data.image_management.mapper.ImageManagementItemImageMapper;
import com.mercaso.data.image_management.repository.ImageManagementImageRepository;
import com.mercaso.data.image_management.repository.ImageManagementItemImageRepository;
import com.mercaso.data.image_management.service.ImageManagementImageService;
import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.data.master_catalog.constants.CommonConstants;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.client.dto.ItemDto;
import com.mercaso.ims.client.dto.ItemUPCDto;
import com.mercaso.ims.client.dto.ItemUPCDto.ItemUpcTypeEnum;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import jakarta.activation.MimetypesFileTypeMap;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class ImageManagementImageServiceImpl implements ImageManagementImageService {

  @Value("${mercaso.document.operations.storage.image-management.root-folder}")
  private String rootFolder;

  private final ImsClientAdaptor imsClientAdaptor;

  private final DocumentOperations documentOperations;

  private final ImageManagementImageRepository imageManagementRepository;

  private final ImageManagementItemImageRepository imageManagementItemImageRepository;

  private final ImageManagementItemImageMapper imageManagementItemImageMapper;

  private static final String IMAGE_PRIMARY_FLAG = "Front_2";
  private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
  private static final String ITEM_UPC_TYPE = "EACH_UPC";
  private static final String SEPARATOR = "/";

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String uploadAndSave(String shotAtStr, String fileName,
      ImageTypeEnum imageType, MultipartFile file) {

    // valid image path format
    validFileName(fileName);
    Instant shotAt = validSafeParseDate(shotAtStr, DEFAULT_DATE_FORMAT);
    fileName = fileName.replaceAll("([a-zA-Z]+)-(\\d+)", "$1_$2");
    String upc = fileName.substring(0, fileName.indexOf("_"));
    String imagePath = shotAtStr + SEPARATOR + upc + SEPARATOR + fileName;
    String angel = getAngel(imagePath, upc, fileName);
    String mimeType = isImageByMimeType(fileName);
    Boolean isPrimary = IMAGE_PRIMARY_FLAG.equals(angel);

    String imageKey = rootFolder + imageType.name() + SEPARATOR + imagePath;
    Boolean exists = imageManagementRepository.existsByFilePath(imageKey);
    if (exists) {
      throw new ImageManagementImageException(ErrorCodeEnums.IMAGE_MANAGEMENT_ITEM_IMAGE_UPLOAD_ALREADY.getCode(),
          "Image of path:" + imagePath + " already exists");
    }

    try {
      UploadDocumentRequest documentRequest = UploadDocumentRequest
          .builder()
          .documentName(imageKey)
          .content(file.getBytes())
          .build();
      documentOperations.uploadDocument(documentRequest);
    } catch (Exception e) {
      log.error("Error while uploading image {} which path {}", file.getOriginalFilename(), imageKey, e);
      throw new ImageManagementImageException(ErrorCodeEnums.UPLOAD_IMAGE_MANAGEMENT_ITEM_IMAGE_FAILURE.getCode(),
          "Error while uploading image " + file.getOriginalFilename());
    }

    String userId = SecurityContextUtil.getLoginUserId();
    userId = userId == null ? CommonConstants.SYSTEM_USER_ID : userId;
    ImageManagementImage image = ImageManagementImage.builder()
        .fileName(fileName)
        .filePath(imageKey)
        .shotAt(shotAt)
        .fileSize(file.getSize())
        .mimeType(mimeType)
        .createdBy(userId)
        .build();
    ImageManagementImage save = imageManagementRepository.save(image);

    List<ItemDto> items = imsClientAdaptor.searchItemDetailByUpc(upc);
    Boolean eachFlag = getEachFlag(upc, fileName, items);
    items.forEach(item -> {
      ImageManagementItemImage imageItem = ImageManagementItemImage
          .builder()
          .eachFlag(eachFlag)
          .imageId(save.getId())
          .imageAngel(angel)
          .isPrimary(isPrimary)
          .sku(item.getSkuNumber())
          .imageType(imageType.name())
          .build();
      imageManagementItemImageRepository.save(imageItem);
    });

    return ErrorCodeEnums.COMMON_CODE.getCode();
  }

  private String isImageByMimeType(String filename) {
    String mimeType = new MimetypesFileTypeMap().getContentType(filename);
    if (mimeType != null && mimeType.trim().startsWith("image/")) {
      return mimeType;
    }
    throw new ImageManagementImageException(UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE.getCode(),
        "unsupported mime type" + mimeType);
  }

  private Instant validSafeParseDate(String dateTimeStr, String pattern) {
    DateTimeFormatter formatter = new DateTimeFormatterBuilder().appendPattern(pattern)
        .parseDefaulting(ChronoField.MONTH_OF_YEAR, 1)
        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
        .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
        .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
        .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
        .toFormatter();

    try {
      return LocalDateTime.parse(dateTimeStr, formatter)
          .atZone(ZoneId.systemDefault()).toInstant();
    } catch (DateTimeParseException e) {
      log.warn("Invalid shotAt time {}", dateTimeStr);
      throw new ImageManagementImageException(ErrorCodeEnums.INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_SHOT_AT.getCode(),
          "Invalid shotAt time " + dateTimeStr);
    }

  }

  private String getAngel(String imagePath, String upc, String fileName) {
    String baseName = fileName.substring(0, fileName.lastIndexOf('.'));

    if (baseName.startsWith(upc + "_")) {
      String remaining = baseName.substring(upc.length() + 1);
      if (remaining.contains("_")) {
        if (remaining.indexOf('_') != remaining.lastIndexOf('_')) {
          int lastUnderscore = remaining.lastIndexOf('_');
          String suffix = remaining.substring(lastUnderscore + 1);
          return suffix.matches("\\d+") ? remaining.substring(0, lastUnderscore) : remaining;
        } else {
          return remaining;
        }
      }
    }

    log.warn("Invalid image path {} for the file {}", imagePath, fileName);
    throw new ImageManagementImageException(ErrorCodeEnums.INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_FILE_NAME.getCode(),
        "Invalid image path " + imagePath);
  }

  private Boolean getEachFlag(String upc, String fileName, List<ItemDto> items) {
    Boolean eachFlag = null;
    //if items for current upc only contains one upc type,we get upc type from ims
    List<ItemUpcTypeEnum> upcTypeEnumList = items
        .stream()
        .map(ItemDto::getItemUPCs)
        .filter(Objects::nonNull)
        .flatMap(List::stream)
        .filter(itemUpc -> upc.equals(itemUpc.getUpcNumber()))
        .map(ItemUPCDto::getItemUpcType)
        .toList();
    if (upcTypeEnumList.size() == 1) {
      eachFlag = ITEM_UPC_TYPE.equals(upcTypeEnumList.get(0).getValue());
    } else {
      // if items for current upc contains more upc type,we get upc type from the file name
      String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
      if (baseName.startsWith(upc + "_")) {
        String remaining = baseName.substring(upc.length() + 1);
        if (remaining.contains("_")) {
          if (remaining.indexOf('_') != remaining.lastIndexOf('_')) {
            int lastUnderscore = remaining.lastIndexOf('_');
            String suffix = remaining.substring(lastUnderscore + 1);
            eachFlag = "1".equals(suffix);
          } else {
            eachFlag = false;
          }
        }
      }
    }
    return eachFlag;
  }

  private void validFileName(String imagePath) {
    Pattern pattern = Pattern.compile("^(\\d+)_([a-zA-Z]+)[-_](\\d+)(?:_(\\d+))?\\.[a-zA-Z0-9]+$");
    if (!pattern.matcher(imagePath.trim()).matches()) {
      log.warn("Invalid image path {}", imagePath);
      throw new ImageManagementImageException(ErrorCodeEnums.INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_FILE_NAME.getCode(),
          "Invalid image path " + imagePath);
    }
  }

  @Override
  public Page<ImageManagementItemImageDto> searchImages(ImageSearchRequestDto request) {
    int page = request.getPage() != null ? request.getPage() - 1 : 0;
    int pageSize = request.getPageSize() != null ? request.getPageSize() : 20;
    Sort sort = Sort.unsorted();
    if (request.getSortBy() != null && request.getSortDirection() != null) {
      Sort.Direction direction = "desc".equalsIgnoreCase(request.getSortDirection())
          ? Sort.Direction.DESC : Sort.Direction.ASC;
      sort = Sort.by(direction, request.getSortBy());
    }
    Pageable pageable = PageRequest.of(page, pageSize, sort);
    String imageType = request.getImageType() != null ? request.getImageType().name() : null;

    Page<ImageManagementItemImage> imagePage = imageManagementItemImageRepository.searchImages(
        request.getSku(),
        request.getUpc(),
        imageType,
        request.getImageAngle(),
        request.getIsPrimary(),
        request.getIsEach(),
        pageable
    );

    List<java.util.UUID> imageIds = imagePage.getContent().stream()
        .map(ImageManagementItemImage::getImageId)
        .filter(Objects::nonNull)
        .distinct()
        .toList();
    Map<java.util.UUID, String> imageIdToFilePath = imageIds.isEmpty() ? Map.of() :
        imageManagementRepository.findAllById(imageIds).stream()
            .collect(java.util.stream.Collectors.toMap(
                ImageManagementImage::getId,
                ImageManagementImage::getFilePath
            ));

    return imagePage.map(itemImage -> {
      ImageManagementItemImageDto dto = imageManagementItemImageMapper.toDto(itemImage);
      if (itemImage.getImageId() != null) {
        String filePath = imageIdToFilePath.get(itemImage.getImageId());
        if (filePath != null) {
          dto.setImageUrl(filePath);
        }
      }
      return dto;
    });
  }

}
