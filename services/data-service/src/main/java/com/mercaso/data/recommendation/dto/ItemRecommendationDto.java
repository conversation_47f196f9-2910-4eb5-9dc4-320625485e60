package com.mercaso.data.recommendation.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemRecommendationDto {

  private String storeId;
  private String productId;
  private String sku;
  private RecommendationReasonDto reason;
  private String version;
  private String departmentId;
  private String department;
  private String highPrice;
  private String lowPrice;
  private String avgPrice;
  private BigDecimal cost;
}
