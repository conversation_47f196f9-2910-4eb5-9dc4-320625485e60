package com.mercaso.data.master_catalog.dto;

import jakarta.validation.constraints.AssertTrue;
import java.util.stream.Stream;
import lombok.Data;

@Data
public class SearchAssociatedProductsRequest {

    private int page = 1;
    private int pageSize = 20;
    private String upc;
    private String description;

    @AssertTrue(message = "The search condition is invalid, only can use one of the following: upc, description")
    public boolean isSearchConditionValid() {
        long count = Stream.of(upc, description)
            .filter(s -> s != null && !s.isEmpty())
            .count();
        return count == 1 || count == 0;
    }
}
