package com.mercaso.data.master_catalog.adaptor;

import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import java.util.List;
import java.util.UUID;
import org.springframework.web.multipart.MultipartFile;

public interface ExternalApiAdapter {

    List<String> getUpcsFromExternalApi(String description);

    List<String> getUpcsFromExternalApiByImage(MultipartFile image);

    void sendInitialProcessing(UUID taskId, List<MasterCatalogRawData> data);

    void sendFinalProcessing(UUID taskId, List<MasterCatalogPotentiallyDuplicateRawDataDto> data);
}
