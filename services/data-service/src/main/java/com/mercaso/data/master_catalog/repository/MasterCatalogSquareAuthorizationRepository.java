package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MasterCatalogSquareAuthorizationRepository extends JpaRepository<MasterCatalogSquareAuthorization, UUID> {

    MasterCatalogSquareAuthorization findByState(UUID state);

    MasterCatalogSquareAuthorization findByStoreId(UUID storeId);
}
