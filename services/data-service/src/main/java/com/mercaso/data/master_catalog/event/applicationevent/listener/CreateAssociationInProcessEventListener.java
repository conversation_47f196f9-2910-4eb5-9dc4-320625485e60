package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.model.domain.CreateAssociationInProcessEvent;
import com.mercaso.data.master_catalog.event.payload.domain.CreateAssociationInProcessPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyAssociateProductService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateAssociationInProcessEventListener implements
    ApplicationEventListener<CreateAssociationInProcessEvent, CreateAssociationInProcessPayload> {

  private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
  private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
  private final MasterCatalogPotentiallyAssociateProductService potentiallyAssociateProductService;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void handleEvent(CreateAssociationInProcessEvent event) {
    log.info("CREATE_ASSOCIATE_IN_PROGRESS listener start handle event.");

    CreateAssociationInProcessPayload payload = event.getPayload();
    MasterCatalogBatchJobDto jobDto = payload.getData();
    List<List<UUID>> productIds = payload.getProductIds();
    UUID jobId = jobDto.getId();
    MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(jobDto);
    potentiallyAssociateProductService.processPotentiallyAssociateProduct(jobId, productIds);

    updateJobStatus(job);
  }

  void updateJobStatus(MasterCatalogBatchJob job) {
    job.setStatus(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS);
    masterCatalogBatchJobRepository.save(job);
    log.info("Job {} create association in process with status {}", job.getId(), job.getStatus());
  }
}
