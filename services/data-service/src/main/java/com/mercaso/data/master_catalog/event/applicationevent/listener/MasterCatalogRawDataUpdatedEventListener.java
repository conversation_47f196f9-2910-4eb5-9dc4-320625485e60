package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.model.domain.MasterCatalogRawDataUpdatedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataUpdatedPayload;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;


@Slf4j
@Component
@RequiredArgsConstructor
public class MasterCatalogRawDataUpdatedEventListener implements
    ApplicationEventListener<MasterCatalogRawDataUpdatedEvent, MasterCatalogRawDataUpdatedPayload> {

    private final MasterCatalogRawDataDuplicationRepository duplicationRepository;
    private final MasterCatalogProductRepository productRepository;
    private final MasterCatalogProductAssociationRepository productAssociationRepository;
    private final MasterCatalogRawDataService rawDataService;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void handleEvent(MasterCatalogRawDataUpdatedEvent event) {
        transactionTemplate.execute(status -> {
            log.info("MASTER_CATALOG_RAW_DATA_UPDATED listener start handle event.");

            MasterCatalogRawDataUpdatedPayload payload = event.getPayload();
            UUID rawDataId = payload.getRawDataId();

            log.info("Processing MasterCatalogRawDataUpdated event for rawDataId: {}", rawDataId);

            // Step 1: Check if there exists a duplication group for this UPC
            List<MasterCatalogRawDataDuplication> duplications = duplicationRepository.findByUpc(payload.getData().getUpc());

            if (!duplications.isEmpty()) {
                // Duplication group exists
                handleDuplicationGroupExists(duplications);
            } else {
                // No duplication group exists
                handleNoDuplicationGroup(rawDataId);
            }

            // Step 4: Sync raw data by UPC after cleanup is complete
            rawDataService.syncMasterCatalogRawData(payload.getData());

            log.info("Successfully processed MasterCatalogRawDataUpdated event for rawDataId: {}", rawDataId);
            return null;
        });
    }
    
    private void handleDuplicationGroupExists(List<MasterCatalogRawDataDuplication> duplications) {
        // Get the duplication group ID
        UUID groupId = duplications.getFirst().getDuplicationGroup();
        log.info("Found duplication group: {}", groupId);
        
        // Delete duplication data by groupId
        List<MasterCatalogRawDataDuplication> allDuplicationsInGroup = duplicationRepository.findAllByDuplicationGroup(groupId);
        duplicationRepository.deleteAll(allDuplicationsInGroup);
        log.info("Deleted {} duplication records for group: {}", allDuplicationsInGroup.size(), groupId);
        
        // Step 2: Get all UPCs in this group and delete products
        List<String> upcsInGroup = allDuplicationsInGroup.stream()
            .map(MasterCatalogRawDataDuplication::getUpc)
            .distinct()
            .toList();
        
        List<MasterCatalogProduct> productsToDelete = productRepository.findAllByUpcIn(upcsInGroup);
        if (!productsToDelete.isEmpty()) {
            productRepository.deleteAll(productsToDelete);
            log.info("Deleted {} products for UPCs in group: {}", productsToDelete.size(), groupId);
            
            // Step 3: Clean up product associations
            cleanupProductAssociations(productsToDelete);
        }
    }
    
    private void handleNoDuplicationGroup(UUID rawDataId) {
        log.info("No duplication group found, deleting products by rawDataId: {}", rawDataId);
        
        // Step 2: Delete products by rawDataId
        Optional<MasterCatalogProduct> productsToDelete = productRepository.findByMasterCatalogRawDataId(rawDataId);
        if (productsToDelete.isPresent()) {
            
            productRepository.delete(productsToDelete.get());
            log.info("Deleted product by rawDataId: {}", rawDataId);
            
            // Step 3: Clean up product associations
            cleanupProductAssociations(productsToDelete.stream().toList());
        }
    }
    
    private void cleanupProductAssociations(List<MasterCatalogProduct> deletedProducts) {
        
        for (MasterCatalogProduct product : deletedProducts) {
            Boolean isAssociated = product.getAssociated();
            if (isAssociated != null && isAssociated) {
                String upc = product.getUpc();
                if (upc != null) {
                    // Find association records for this UPC
                    List<MasterCatalogProductAssociation> associations = productAssociationRepository.findAllByUpcIn(List.of(upc));
                    
                    for (MasterCatalogProductAssociation association : associations) {
                        UUID associationGroup = association.getAssociationGroup();
                        
                        // Delete this UPC from the association group
                        productAssociationRepository.delete(association);
                        log.info("Removed UPC {} from association group: {}", upc, associationGroup);
                        
                        // Check if the group has only one record left after deletion
                        List<MasterCatalogProductAssociation> remainingAssociations = 
                            productAssociationRepository.findByAssociationGroup(associationGroup);
                        
                        if (remainingAssociations.size() == 1) {
                            // Delete the last remaining record as a group with only one member is meaningless
                            productAssociationRepository.deleteAll(remainingAssociations);
                            log.info("Deleted association group {} as it had only 1 record left", associationGroup);
                        }
                    }
                }
            } else {
                log.debug("Product {} is not associated, skipping association cleanup", product.getId());
            }
        }
        
        log.info("Completed product association cleanup");
    }
}
