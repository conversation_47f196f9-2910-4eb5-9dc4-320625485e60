package com.mercaso.data.image_management.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import com.mercaso.data.master_catalog.entity.BaseEntity;
import java.util.UUID;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "image_management_item_image")
public class ImageManagementItemImage extends BaseEntity {

    @NotNull
    @Column(name = "image_id", nullable = false, unique = true)
    private UUID imageId;

    @NotNull
    @Size(max = 255)
    @Column(name = "image_angel", nullable = false)
    private String imageAngel;

    @Size(max = 255)
    @Column(name = "sku")
    private String sku;

    @Size(max = 255)
    @Column(name = "upc")
    private String upc;

    @Column(name = "is_each")
    private Boolean eachFlag;

    @NotNull
    @Column(name = "is_primary", nullable = false)
    private Boolean isPrimary;

    @NotNull
    @Size(max = 255)
    @Column(name = "image_type", nullable = false)
    private String imageType;
} 