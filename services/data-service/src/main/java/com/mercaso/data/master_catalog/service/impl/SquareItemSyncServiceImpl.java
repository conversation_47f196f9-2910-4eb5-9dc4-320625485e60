package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.DAYS_IN_ONE_YEAR;
import static com.mercaso.data.master_catalog.constants.SquareConstants.SQUARE_API_LIMIT;
import static com.mercaso.data.master_catalog.enums.RawDataStatus.DRAFT;
import static com.mercaso.data.master_catalog.enums.square.CatalogObjectTypes.CATEGORY;
import static com.mercaso.data.master_catalog.enums.square.CatalogObjectTypes.IMAGE;
import static com.mercaso.data.master_catalog.enums.square.CatalogObjectTypes.ITEM;
import static com.mercaso.data.utils.FileUtils.download;

import com.alibaba.excel.util.DateUtils;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.dto.external.UploadDocumentRequestDto;
import com.mercaso.data.master_catalog.dto.square.ImageInfoDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsRequestDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsResponseDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.enums.square.SyncEntity;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.SquareItemSyncService;
import com.mercaso.data.utils.SerializationUtils;
import com.squareup.square.models.CatalogCategory;
import com.squareup.square.models.CatalogItem;
import com.squareup.square.models.CatalogItemVariation;
import com.squareup.square.models.CatalogObject;
import com.squareup.square.models.CatalogObjectCategory;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class SquareItemSyncServiceImpl implements SquareItemSyncService {

    private final SquareApiAdapter squareApiAdapter;
    private final MasterCatalogRawDataRepository rawDataRepository;
    private final MasterCatalogImageRepository imageRepository;
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository;
    private final S3OperationAdapter s3OperationAdapter;
    private final MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository;
    private final MasterCatalogSquareVariationMappingRepository masterCatalogSquareVariationMappingRepository;

    private static final int BATCH_SIZE = 500;

    @Transactional
    @Override
    public List<CatalogObject> syncItems(SquareDataSyncRequest request) {

        if (request.getSyncEntity() != null && request.getSyncEntity() != SyncEntity.ITEM) {
            log.info("Skipping item synchronization for request: {}", request);
            return List.of();
        }

        UUID storeId = request.getStoreId();
        log.info("Synchronized square items, starting for store ID: {}", storeId);
        Instant latestSyncTime = this.retrieveLatestSyncTime(storeId);

        SearchCatalogObjectsRequestDto requestDto = this.buildSearchRequest(latestSyncTime);
        SearchCatalogObjectsResponseDto response = squareApiAdapter.searchCatalogObjects(storeId, requestDto);

        if (CollectionUtils.isEmpty(response.getObjects())) {
            log.info("Synchronized square items, no catalog objects found for store ID: {}", storeId);
            return null;
        }

        MasterCatalogSquareRawData savedMasterCatalogSquareRawData = masterCatalogSquareRawDataRepository.save(
            MasterCatalogSquareRawData.builder()
                .data(SerializationUtils.toTree(response))
                .build());

        this.processCatalogObjects(storeId, response, latestSyncTime, savedMasterCatalogSquareRawData.getId());
        log.info("Synchronized square items, successfully for store ID: {}", storeId);
        return response.getObjects();
    }

    private Instant retrieveLatestSyncTime(UUID storeId) {
        return rawDataRepository.findTopByStoreIdOrderByCreatedAtDesc(storeId)
            .map(MasterCatalogRawData::getCreatedAt)
            .orElseGet(() -> {
                log.info("No data found for store ID: {}. Performing full synchronization.", storeId);
                return Instant.now().minus(DAYS_IN_ONE_YEAR, ChronoUnit.DAYS);
            });
    }

    private SearchCatalogObjectsRequestDto buildSearchRequest(Instant latestSyncTime) {
        return SearchCatalogObjectsRequestDto.builder()
            .objectTypes(Collections.singletonList(ITEM.name()))
            .includeRelatedObjects(true)
            .beginTime(latestSyncTime.toString())
            .limit(SQUARE_API_LIMIT)
            .build();
    }

    private void processCatalogObjects(UUID storeId, SearchCatalogObjectsResponseDto response, Instant latestSyncTime,
        UUID sourceId) {
        Map<String, CatalogObject> itemsMap = this.filterObjectsByType(response.getObjects(), ITEM.name());
        Map<String, CatalogObject> imagesMap = this.filterObjectsByType(response.getRelatedObjects(), IMAGE.name());
        Map<String, CatalogObject> categoriesMap = this.filterObjectsByType(response.getRelatedObjects(), CATEGORY.name());

        List<MasterCatalogRawData> rawDataList = this.extractRawData(storeId, itemsMap, categoriesMap);
        if (rawDataList.isEmpty()) {
            log.info("Synchronized square items, no raw data extracted for store ID: {}", storeId);
            return;
        }

        // Set source ID for all raw data records
        rawDataList.forEach(r -> r.setSourceId(String.valueOf(sourceId)));

        Map<String, List<CatalogObject>> itemToVariationsMap = this.mapItemsToVariations(itemsMap);
        Map<String, String> imageUrlMap = getImageUrlMap(imagesMap);

        List<ImageInfoDto> imagesInfoList = this.downloadImages(imageUrlMap);

        Map<String, List<ImageInfoDto>> variationIdToImageInfoMap = this.mapVariationIdToImageInfo(itemToVariationsMap,
            imagesInfoList, itemsMap);

        List<MasterCatalogImage> imageList = this.uploadImagesToS3(variationIdToImageInfoMap, rawDataList);
        this.saveImages(imageList);

        Map<String, UUID> variationIdToRawDataIdMap = this.mapVariationIdsToRawDataIds(rawDataList);

        Map<UUID, String> rawDataIdToVariationIdMap = this.mapRawDataIdsToVariationIds(rawDataList);

        List<String> newVariationIds = this.filterNewVariationIds(variationIdToRawDataIdMap);

        Map<String, UUID> existingVariationIdsToRasDataIdMap = this.findExistingVariationIds(storeId, newVariationIds);

        rawDataList.forEach(rawData -> {

            String variationId = rawDataIdToVariationIdMap.get(rawData.getId());

            if (existingVariationIdsToRasDataIdMap.containsKey(variationId)) {

                rawData.setId(existingVariationIdsToRasDataIdMap.get(variationId));
            }
        });

        List<MasterCatalogRawData> savedRawDataList = rawDataRepository.saveAll(rawDataList);
        log.info("Synchronized square items, inserted {} raw data records for store ID: {}", savedRawDataList.size(), storeId);

        int i = this.saveVariationMappings(storeId, variationIdToRawDataIdMap, latestSyncTime);

        if (i != rawDataList.size()) {
            log.warn("Synchronized square items, inserted {} variation mapping records, expected {} for store ID: {}",
                i, rawDataList.size(), storeId);
        }
    }

    private Map<String, String> getImageUrlMap(Map<String, CatalogObject> imagesMap) {
        return imagesMap.values().stream()
            .filter(image -> image.getImageData() != null)
            .collect(Collectors.toMap(
                CatalogObject::getId,
                image -> image.getImageData().getUrl()
            ));
    }

    private Map<String, List<ImageInfoDto>> mapVariationIdToImageInfo(Map<String, List<CatalogObject>> itemToVariationsMap,
        List<ImageInfoDto> imageInfoList, Map<String, CatalogObject> itemsMap) {
        if (CollectionUtils.isEmpty(imageInfoList)) {
            return Collections.emptyMap();
        }

        Map<String, String> variationIdToItemIdMap = mapVariationIdToItemId(itemToVariationsMap);

        Map<String, List<ImageInfoDto>> variationToImageInfoMap = new HashMap<>();
        itemToVariationsMap.forEach((itemId, variations) -> variations.forEach(variation -> {

            List<String> imageIds = itemsMap.get(variationIdToItemIdMap.get(variation.getId()))
                .getItemData()
                .getImageIds();
            if (CollectionUtils.isEmpty(imageIds)) {
                return;
            }

            List<ImageInfoDto> images = imageInfoList.stream().filter(image -> imageIds.contains(image.getId())).toList();
            variationToImageInfoMap.put(variation.getId(), images);
        }));
        return variationToImageInfoMap;
    }

    private Map<String, CatalogObject> filterObjectsByType(List<CatalogObject> objects, String type) {
        return objects.stream()
            .filter(obj -> Objects.equals(type, obj.getType()))
            .collect(Collectors.toMap(CatalogObject::getId, Function.identity()));
    }

    private List<MasterCatalogRawData> extractRawData(UUID storeId, Map<String, CatalogObject> items,
        Map<String, CatalogObject> categories) {
        return items.values().stream()
            .filter(this::isNotArchived)
            .flatMap(item -> mapItemToRawData(storeId, item, categories))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private Stream<MasterCatalogRawData> mapItemToRawData(UUID storeId, CatalogObject item,
        Map<String, CatalogObject> categories) {
        CatalogItem itemData = item.getItemData();
        String itemName = itemData.getName();
        String categoryName = retrieveCategoryName(itemData, categories);
        List<CatalogObject> variations = getVariations(itemData);

        return variations.stream()
            .map(variation -> {
                String upc = extractUpcFromVariation(variation);

                if (StringUtils.isBlank(upc)) {
                    log.warn("Synchronized square items, no UPC found for variation ID: {}, item ID: {}",
                        variation.getId(),
                        item.getId());
                    return null;
                }

                String sku = extractSkuFromVariation(variation);
                Long price = extractPriceFromVariation(variation);
                Instant creationTime = Instant.parse(variation.getUpdatedAt());
                String sourceId = item.getId();

                return MasterCatalogRawData.builder()
                    .id(UUID.randomUUID())
                    .storeId(storeId)
                    .sourceId(sourceId)
                    .upc(upc)
                    .skuNumber(sku)
                    .name(itemName)
                    .description(itemName)
                    .category(categoryName)
                    .createdAt(creationTime)
                    .status(DRAFT.name())
                    .variationId(variation.getId())
                    .price(BigDecimal.valueOf(price))
                    .packageType(PackageType.INDIVIDUAL)
                    .packageSize(1)
                    .build();
            });
    }

    private Map<String, String> mapVariationIdToItemId(Map<String, List<CatalogObject>> itemToVariationsMap) {
        Map<String, String> variationIdToItemIdMap = new HashMap<>();

        Map<String, List<String>> itemIdToVariationIdsMap = mapItemIdsToVariationIds(itemToVariationsMap);

        itemIdToVariationIdsMap.forEach((itemId, variationIds) ->
            variationIds.forEach(variationId -> variationIdToItemIdMap.put(variationId, itemId))
        );
        return variationIdToItemIdMap;
    }

    private List<ImageInfoDto> downloadImages(Map<String, String> imageUrlMap) {

        return imageUrlMap.entrySet().parallelStream()
            .map(entry -> {
                String key = entry.getKey();
                String url = entry.getValue();
                try {
                    byte[] imageData = download(url);
                    log.info("Download successful: {}", url);
                    return ImageInfoDto.builder()
                        .id(key)
                        .originalUrl(url)
                        .content(imageData)
                        .extension(FilenameUtils.getExtension(url))
                        .build();
                } catch (IOException e) {
                    log.warn("Download failed: {} error: {}", url, e.getMessage());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private List<MasterCatalogImage> uploadImagesToS3(Map<String, List<ImageInfoDto>> variationIdToImageInfoMap,
        List<MasterCatalogRawData> rawDataList) {

        if (CollectionUtils.isEmpty(variationIdToImageInfoMap)) {
            log.info("Synchronized square items, no rawDataImages to upload.");
            return Collections.emptyList();
        }

        List<MasterCatalogImage> rawDataImages = new ArrayList<>();

        rawDataList.forEach(rd -> {
            List<ImageInfoDto> variationImages = variationIdToImageInfoMap.get(rd.getVariationId());
            if (CollectionUtils.isEmpty(variationImages)) {
                log.info("Synchronized square items, no images found for variation ID: {}， raw data ID: {}",
                    rd.getVariationId(),
                    rd.getId());

            } else {
                variationImages.forEach(info -> {
                    String dateStr = DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14);

                    String imageName =
                        StringUtils.right(rd.getStoreId().toString(), 6) + "_" + rd.getUpc() + "_" + dateStr + "."
                            + info.getExtension();
                    UploadDocumentRequestDto s3UploadDto = UploadDocumentRequestDto.builder()
                        .documentName(imageName)
                        .content(info.getContent())
                        .build();
                    s3OperationAdapter.upload(s3UploadDto);

                    rawDataImages.add(MasterCatalogImage.builder()
                        .masterCatalogRawDataId(rd.getId())
                        .imagePath(imageName)
                        .primaryImage(false)
                        .build());
                });
            }

        });
        return rawDataImages;
    }


    private void saveImages(List<MasterCatalogImage> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            log.info("Synchronized square items, no images to save.");
            return;
        }
        imageRepository.saveAll(imageList);
        log.info("Synchronized square items, inserted {} image records.", imageList.size());
    }

    private int saveVariationMappings(UUID storeId, Map<String, UUID> variationIdToRawDataIdMap, Instant latestSyncTime) {

        List<String> newVariationIds = this.filterNewVariationIds(variationIdToRawDataIdMap);

        Map<String, UUID> existingVariationIdsToRasDataIdMap = this.findExistingVariationIds(storeId, newVariationIds);
        List<String> existingVariationIds = existingVariationIdsToRasDataIdMap.keySet().stream().toList();
        List<MasterCatalogSquareVariationMapping> variationMappings = this.buildMasterCatalogSquareVariationMappings(
            variationIdToRawDataIdMap,
            existingVariationIds);

        variationMappingRepository.saveAll(variationMappings);
        log.info("Synchronized square items, inserted {} variation mapping records.", variationMappings.size());
        return variationMappings.size();
    }

    private List<MasterCatalogSquareVariationMapping> buildMasterCatalogSquareVariationMappings(
        Map<String, UUID> variationIdToRawDataIdMap, List<String> existingVariationIds) {

        return variationIdToRawDataIdMap.entrySet().stream()
            .filter(entry -> !existingVariationIds.contains(entry.getKey()))
            .map(entry -> MasterCatalogSquareVariationMapping.builder()
                .variationId(entry.getKey())
                .masterCatalogRawDataId(entry.getValue())
                .build())
            .collect(Collectors.toList());
    }

    private Map<String, UUID> findExistingVariationIds(UUID storeId, List<String> newVariationIds) {

        List<MasterCatalogSquareVariationMapping> variationMappingList = this.findMappingsByVariationIdsPageable(newVariationIds,
            storeId);

        return variationMappingList.stream()
            .collect(Collectors.toMap(MasterCatalogSquareVariationMapping::getVariationId,
                MasterCatalogSquareVariationMapping::getMasterCatalogRawDataId));
    }

    private List<String> filterNewVariationIds(Map<String, UUID> variationIdToRawDataIdMap) {

        return variationIdToRawDataIdMap.keySet().stream().toList();
    }

    private String retrieveCategoryName(CatalogItem itemData, Map<String, CatalogObject> categoryMap) {
        return Optional.ofNullable(itemData.getReportingCategory())
            .map(CatalogObjectCategory::getId)
            .map(categoryMap::get)
            .map(CatalogObject::getCategoryData)
            .map(CatalogCategory::getName)
            .orElse(null);
    }

    private String extractUpcFromVariation(CatalogObject variation) {
        return Optional.ofNullable(variation.getItemVariationData())
            .map(CatalogItemVariation::getUpc)
            .orElse(null);
    }

    private String extractSkuFromVariation(CatalogObject variation) {
        return Optional.ofNullable(variation.getItemVariationData())
            .map(CatalogItemVariation::getSku)
            .orElse(null);
    }

    private Long extractPriceFromVariation(CatalogObject variation) {
        return Optional.ofNullable(variation.getItemVariationData())
            .filter(v -> v.getPriceMoney() != null)
            .map(CatalogItemVariation::getPriceMoney)
            .map(money -> money.getAmount() / 100)
            .orElse(0L);
    }

    private Map<String, List<CatalogObject>> mapItemsToVariations(Map<String, CatalogObject> itemsMap) {
        return itemsMap.values().stream()
            .filter(this::isNotArchived)
            .collect(Collectors.toMap(
                CatalogObject::getId,
                item -> getVariations(item.getItemData())
            ));
    }

    private Map<String, List<String>> mapItemIdsToVariationIds(Map<String, List<CatalogObject>> itemToVariationMap) {
        return itemToVariationMap.entrySet().stream()
            .filter(entry -> entry.getValue() != null)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream()
                    .map(CatalogObject::getId)
                    .collect(Collectors.toList())
            ));
    }

    private Map<String, UUID> mapVariationIdsToRawDataIds(List<MasterCatalogRawData> rawDataList) {

        return rawDataList.stream()
            .collect(Collectors.toMap(
                MasterCatalogRawData::getVariationId,
                MasterCatalogRawData::getId
            ));
    }

    private Map<UUID, String> mapRawDataIdsToVariationIds(List<MasterCatalogRawData> rawDataList) {

        return rawDataList.stream()
            .collect(Collectors.toMap(
                MasterCatalogRawData::getId,
                MasterCatalogRawData::getVariationId
            ));
    }


    private boolean isNotArchived(CatalogObject item) {
        return BooleanUtils.isNotTrue(item.getItemData().getIsArchived());
    }

    private List<CatalogObject> getVariations(CatalogItem itemData) {
        return Optional.ofNullable(itemData.getVariations()).orElse(Collections.emptyList());
    }

    private List<MasterCatalogSquareVariationMapping> findMappingsByVariationIdsPageable(List<String> variationIds,
        UUID storeId) {
        if (variationIds == null || variationIds.isEmpty()) {
            return Collections.emptyList();
        }

        return partitionList(variationIds, BATCH_SIZE).stream()
            .flatMap(batch -> masterCatalogSquareVariationMappingRepository.findByVariationIdsAndStoreIdNative(batch, storeId)
                .stream())
            .map(this::mapToEntity)
            .collect(Collectors.toList());
    }

    private MasterCatalogSquareVariationMapping mapToEntity(Object[] row) {
        MasterCatalogSquareVariationMapping dto = new MasterCatalogSquareVariationMapping();
        dto.setId((UUID) row[0]);
        dto.setVariationId((String) row[1]);
        dto.setMasterCatalogRawDataId((UUID) row[2]);
        return dto;
    }

    private <T> List<List<T>> partitionList(List<T> list, int size) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitions;
    }
}
