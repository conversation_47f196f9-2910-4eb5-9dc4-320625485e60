package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ReplenishmentRecommendationRepository extends JpaRepository<ReplenishmentRecommendation, UUID>,
    JpaSpecificationExecutor<ReplenishmentRecommendation> {

    @Query("SELECT MAX(m.batchNumber) FROM ReplenishmentRecommendation m WHERE m.storeId = :storeId")
    String findLatestBatchNumberByStoreId(@Param("storeId") UUID storeId);

    @Query("SELECT m FROM ReplenishmentRecommendation m WHERE m.storeId = :storeId AND m.batchNumber = :batchNumber")
    Optional<List<ReplenishmentRecommendation>> findByStoreIdAndBatchNumber(@Param("storeId") UUID storeId,
        @Param("batchNumber") String batchNumber);

    void deleteByStoreIdAndBatchNumber(UUID storeId, String batchNumber);
}
