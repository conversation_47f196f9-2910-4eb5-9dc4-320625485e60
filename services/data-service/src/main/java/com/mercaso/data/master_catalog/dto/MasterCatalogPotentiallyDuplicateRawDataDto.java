package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData}
 */
@AllArgsConstructor
@Getter
@Setter
@Builder
@NoArgsConstructor
public class MasterCatalogPotentiallyDuplicateRawDataDto extends BaseDto {

    private UUID id;
    @NotNull
    private UUID rawDataId;
    private String upc;
    private String name;
    private String primaryVendor;
    private List<String> images;
    private UUID potentiallyDuplicateRawDataId;
    private String potentiallyDuplicateUpc;
    private String potentiallyDuplicateName;
    private String potentiallyDuplicateVendor;
    private List<String> potentiallyDuplicateImages;
    private PotentiallyDuplicateRawDataStatus status;
    @NotNull
    private UUID jobId;
    private UUID taskId;
    private Instant createdAt;
    private String createdBy;
    private Instant updatedAt;
    private String updatedBy;
    private Boolean duplicated = Boolean.FALSE;

    public boolean isReviewed() {
        return this.status == PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED
                || this.status == PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED;
    }
}
