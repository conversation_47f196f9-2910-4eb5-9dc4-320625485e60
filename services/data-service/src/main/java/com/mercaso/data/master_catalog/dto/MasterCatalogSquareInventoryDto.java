package com.mercaso.data.master_catalog.dto;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogSquareInventoryDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private String state;
    private Integer quantity;
    private UUID masterCatalogRawDataId;
}