package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.squareup.square.models.ObtainTokenResponse;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface SquareObtainTokenResponseMapper {

    SquareObtainTokenResponse from(ObtainTokenResponse obtainTokenResponse);
}
