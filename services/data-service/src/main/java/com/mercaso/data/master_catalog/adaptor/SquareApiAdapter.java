package com.mercaso.data.master_catalog.adaptor;

import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryChangesRequestDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryCountsRequestDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveOrdersRequestDto;
import com.mercaso.data.master_catalog.dto.square.InventoryChangeDto;
import com.mercaso.data.master_catalog.dto.square.InventoryCountDto;
import com.mercaso.data.master_catalog.dto.square.OrdersDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsRequestDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsResponseDto;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface SquareApiAdapter {

    SquareObtainTokenResponse obtainTokenByAuthorizationCode(String code, String applicationId, String applicationSecret);

    SquareObtainTokenResponse obtainTokenByRefreshToken(String refreshToken, String applicationId, String applicationSecret);

    SearchCatalogObjectsResponseDto searchCatalogObjects(UUID storeId, SearchCatalogObjectsRequestDto request);

    List<InventoryCountDto> batchRetrieveInventoryCounts(UUID storeId, BatchRetrieveInventoryCountsRequestDto request);

    List<InventoryChangeDto> batchRetrieveInventoryChanges(UUID storeId, BatchRetrieveInventoryChangesRequestDto request);

    List<OrdersDto> batchRetrieveOrders(UUID storeId, BatchRetrieveOrdersRequestDto request);

    // List locations
    List<MasterCatalogLocationDto> listLocations(UUID storeId);

    /**
     * Need permission: INVENTORY_WRITE
     * Batch adjust inventory for multiple catalog objects across multiple locations
     *
     * @param storeId store ID
     * @param locationIds list of location IDs
     * @param changes Map of catalogObjectId to quantity delta
     */
    void batchChangeInventory(UUID storeId, List<String> locationIds, Map<String, Integer> changes);
}
