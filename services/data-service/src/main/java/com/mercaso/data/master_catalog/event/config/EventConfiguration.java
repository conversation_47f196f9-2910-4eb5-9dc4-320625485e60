package com.mercaso.data.master_catalog.event.config;

import com.mercaso.data.master_catalog.event.annotation.BusinessEntityIdentifier;
import com.mercaso.data.master_catalog.event.core.EventTypeRegistry;
import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Event configuration class, provides event processing related configuration
 */
@Slf4j
@RequiredArgsConstructor
@Configuration
@EnableAsync
@EnableRetry
public class EventConfiguration {

    private final EventTypeRegistry eventTypeRegistry;

    @PostConstruct
    public void validatePayloadAnnotations() {
        eventTypeRegistry.getAllEventTypes().forEach(eventType -> {
            Class<?> payloadClass = eventType.getPayloadClass();
            long annotatedFields = Arrays.stream(payloadClass.getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(BusinessEntityIdentifier.class))
                .count();

            if (annotatedFields == 0) {
                log.warn("Payload class {} has no @BusinessEntityIdentifier fields",
                    payloadClass.getSimpleName());
            }
        });
    }
} 