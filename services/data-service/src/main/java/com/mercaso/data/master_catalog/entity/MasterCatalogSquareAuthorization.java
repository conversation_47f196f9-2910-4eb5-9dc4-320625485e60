package com.mercaso.data.master_catalog.entity;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.Type;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_square_authorization")
@SQLRestriction("deleted_at is null")
public class MasterCatalogSquareAuthorization extends BaseEntity {

    @Column(name = "state")
    private UUID state;

    @Column(name = "store_id")
    private UUID storeId;

    @Column(name = "permissions")
    private String permissions;

    @Column(name = "encrypted_access_token")
    private String encryptedAccessToken;

    @Column(name = "encrypted_refresh_token")
    private String encryptedRefreshToken;

    @Column(name = "access_token_expires_at")
    private Instant accessTokenExpiresAt;

    @Column(name = "encrypted_application_id")
    private String encryptedApplicationId;

    @Column(name = "encrypted_application_secret")
    private String encryptedApplicationSecret;

    @Column(name = "deleted_at")
    private Instant deletedAt;
}
