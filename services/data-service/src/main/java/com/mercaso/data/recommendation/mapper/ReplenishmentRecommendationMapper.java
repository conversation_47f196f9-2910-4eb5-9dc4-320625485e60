package com.mercaso.data.recommendation.mapper;

import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ReplenishmentRecommendationMapper {

  @Mapping(source = "lastOrderTime", target = "lastPurchaseTime")
  @Mapping(source = "lastOrderQuantity", target = "lastPurchaseQuantity")
  ReplenishmentRecommendationDto toDto(ReplenishmentRecommendation replenishmentRecommendation);
}