package com.mercaso.data.master_catalog.adaptor.impl;

import com.mercaso.data.master_catalog.adaptor.WmsClientAdaptor;
import com.mercaso.wms.client.api.SearchInventoryStockResourceApi;
import com.mercaso.wms.client.dto.ResultInventoryStockDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

@Slf4j
@Component
@AllArgsConstructor
public class WmsClientAdaptorImpl implements WmsClientAdaptor {

    private final SearchInventoryStockResourceApi searchInventoryStockResourceApi;

    public Optional<ResultInventoryStockDto> searchInventoryStockBySku(String sku) {
        try {
            ResultInventoryStockDto resultInventoryStockDto = searchInventoryStockResourceApi.search5(1, 50,
                Collections.singletonList(sku), null, "AVAILABLE", null, null, null, null);
            if (resultInventoryStockDto != null && resultInventoryStockDto.getData() != null
                && !resultInventoryStockDto.getData().isEmpty()) {
                return Optional.of(resultInventoryStockDto);
            }
        } catch (Exception e) {
            log.error("Error searching inventory stock by SKU: {}", sku, e);
        }
        return Optional.empty();
    }
}
