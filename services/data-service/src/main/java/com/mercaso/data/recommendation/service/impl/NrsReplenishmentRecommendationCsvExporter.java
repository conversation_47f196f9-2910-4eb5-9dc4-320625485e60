package com.mercaso.data.recommendation.service.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.REPLENISHMENT_STORE_TO_CUSTOM;

import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import com.mercaso.data.recommendation.service.ReplenishmentRecommendationCsvExporter;
import com.opencsv.CSVWriter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.stereotype.Component;

@Component
public class NrsReplenishmentRecommendationCsvExporter implements ReplenishmentRecommendationCsvExporter {

    @Override
    public void writeHeader(CSVWriter csvWriter) {
        String[] header = {"Batch Number", "ID", "Customer ID", "Quantity", "SKU", "Product Name", "UPC",
                "Transaction Qty", "Order Qty", "Inventory(Forecast)"};
        csvWriter.writeNext(header);
    }

    @Override
    public void writeData(CSVWriter csvWriter, List<ReplenishmentRecommendation> forecasts, UUID storeId) {
        forecasts.forEach(forecast -> {
            Map<String, Object> metadata = forecast.getMetadata();
            String transactionQty = getBigDecimalAsString(metadata, "totalTransactionQty");
            String orderQty = getBigDecimalAsString(metadata, "totalPurchaseQty");
            String inventoryForecast = getBigDecimalAsString(metadata, "inventoryQuantity");
            String[] row = {forecast.getBatchNumber(), forecast.getId().toString(),
                    REPLENISHMENT_STORE_TO_CUSTOM.getOrDefault(storeId.toString(), ""),
                    forecast.getRecommendedQuantity().toString(), forecast.getSku(), forecast.getName(),
                    forecast.getUpc(), transactionQty, orderQty, inventoryForecast};
            csvWriter.writeNext(row);
        });
    }
}
