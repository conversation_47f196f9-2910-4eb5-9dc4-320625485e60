package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import com.mercaso.data.master_catalog.dto.SearchAssociatedProductsRequest;
import com.mercaso.data.master_catalog.service.MasterCatalogProductService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/master-catalog/v1/products")
public class MasterCatalogProductsResource {

    private final MasterCatalogProductService masterCatalogProductService;

    @PreAuthorize("hasAnyAuthority('master-catalog:read:products')")
    @GetMapping("/association/search")
    public ResponseEntity<CustomPage<MasterCatalogProductDto>> searchAssociatedProducts(
        @ModelAttribute @Valid SearchAssociatedProductsRequest searchRequest) {

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts(
            searchRequest.getUpc(),
            searchRequest.getDescription(),
            PageRequest.of(searchRequest.getPage() - 1, searchRequest.getPageSize())
        );

        return ResponseEntity.ok(new CustomPage<MasterCatalogProductDto>().build(result));
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:read:products')")
    @GetMapping("/search")
    public ResponseEntity<CustomPage<MasterCatalogProductDto>> searchProducts(
        @RequestParam(value = "upc", required = false) String upc,
        @RequestParam(value = "name", required = false) String name,
        @Min(1) @RequestParam("page") Integer page,
        @Min(1) @RequestParam("pageSize") Integer pageSize) {
        PageRequest pageRequest = PageRequest.of(page - 1, pageSize);
        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchProducts(upc, name, pageRequest);
        return ResponseEntity.ok(new CustomPage<MasterCatalogProductDto>().build(result));
    }
}
