package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.dashboard.DashboardRequest;
import com.mercaso.data.master_catalog.dto.dashboard.InventoryAndReplenishmentTrendDto;
import com.mercaso.data.master_catalog.dto.dashboard.OrderTrendDto;
import java.util.List;

public interface DashboardService {

    List<InventoryAndReplenishmentTrendDto> getInventoryReplenishmentTrend(DashboardRequest dashboardRequest);

    List<OrderTrendDto> getOrderTrend(DashboardRequest dashboardRequest);
}
