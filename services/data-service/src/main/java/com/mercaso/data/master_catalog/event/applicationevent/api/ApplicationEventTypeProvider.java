package com.mercaso.data.master_catalog.event.applicationevent.api;

import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;

/**
 * Event type provider interface, used for enumeration type implementation
 */
public interface ApplicationEventTypeProvider {

    /**
     * Get event type name
     */
    String getEventTypeName();

    /**
     * Get Event Class
     */
    <P extends BusinessEventPayload<?>> Class<? extends BaseApplicationEvent<P>> getApplicationEventClass();
} 