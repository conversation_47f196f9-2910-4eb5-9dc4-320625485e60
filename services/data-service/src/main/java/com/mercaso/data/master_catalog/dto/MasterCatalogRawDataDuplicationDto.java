package com.mercaso.data.master_catalog.dto;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterCatalogRawDataDuplicationDto extends BaseDto {

    private Instant createdAt;
    private Instant updatedAt;
    private String upc;
    private UUID duplicationGroup;
}