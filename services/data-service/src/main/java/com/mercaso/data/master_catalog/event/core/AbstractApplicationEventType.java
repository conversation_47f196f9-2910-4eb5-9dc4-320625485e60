package com.mercaso.data.master_catalog.event.core;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventType;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import java.lang.reflect.Constructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * Abstract base class for event types, providing common implementation
 */
@Slf4j
@Getter
public abstract class AbstractApplicationEventType<P extends BusinessEventPayload<?>, E extends BaseApplicationEvent<P>>
    implements ApplicationEventType<P, E> {

    private final String name;
    private final Class<P> payloadClass;
    private final Class<E> applicationEventClass;
    private final Constructor<E> constructor;
    private final EventPublishConfig eventPublishConfig;

    protected AbstractApplicationEventType(String name,
        Class<P> payloadClass,
        Class<E> applicationEventClass) {
        this.name = name;
        this.payloadClass = payloadClass;
        this.applicationEventClass = applicationEventClass;

        try {
            this.constructor = applicationEventClass.getDeclaredConstructor(Object.class, payloadClass);
            this.constructor.setAccessible(true);
        } catch (NoSuchMethodException e) {
            log.error("Event class must have constructor(Object, {})", payloadClass.getSimpleName(), e);
            throw new IllegalArgumentException(
                "Event class must have a constructor with parameters (Object, " +
                    payloadClass.getSimpleName() + ")", e
            );
        }

        // Read event configuration
        this.eventPublishConfig = applicationEventClass.getAnnotation(EventPublishConfig.class);
    }

    @Override
    public E createEvent(Object source, Object payload) {
        try {
            if (!payloadClass.isInstance(payload)) {
                throw new IllegalArgumentException(
                    "Payload must be of type " + payloadClass.getName() +
                        " but was " + payload.getClass().getName()
                );
            }
            return constructor.newInstance(source, payloadClass.cast(payload));
        } catch (Exception e) {
            log.error("Failed to create event instance", e);
            throw new RuntimeException("Failed to create event instance", e);
        }
    }

    public boolean shouldPersist() {
        return eventPublishConfig == null || eventPublishConfig.persist();
    }

    public boolean shouldPublishLocal() {
        return eventPublishConfig == null || eventPublishConfig.publishLocal();
    }
} 