package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.dto.dashboard.DashboardRequest;
import com.mercaso.data.master_catalog.dto.dashboard.InventoryAndReplenishmentTrendDto;
import com.mercaso.data.master_catalog.dto.dashboard.OrderTrendDto;
import com.mercaso.data.master_catalog.service.DashboardService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/master-catalog/v1/dashboard")
public class DashboardResource {

    private final DashboardService dashboardService;

    @PreAuthorize("hasAnyAuthority('master-catalog:read:dashboard')")
    @GetMapping("/inventory-replenishment-trend")
    public ResponseEntity<List<InventoryAndReplenishmentTrendDto>> inventoryReplenishmentTrend(
        @Valid @ModelAttribute DashboardRequest dashboardRequest) {
        log.info("inventoryReplenishmentTrend Dashboard request: {}", dashboardRequest);
        return ResponseEntity.ok(dashboardService.getInventoryReplenishmentTrend(dashboardRequest));
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:read:dashboard')")
    @GetMapping("/order-trend")
    public ResponseEntity<List<OrderTrendDto>> orderTrend(@Valid @ModelAttribute DashboardRequest dashboardRequest) {
        log.info("orderTrend Dashboard request: {}", dashboardRequest);
        return ResponseEntity.ok(dashboardService.getOrderTrend(dashboardRequest));
    }
}
