package com.mercaso.data.master_catalog.controller.v1;


import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyAssociateProductService;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/master-catalog/v1/potentially-associate-products")
public class MasterCatalogPotentiallyAssociateProductResource {

  private final MasterCatalogPotentiallyAssociateProductService potentiallyAssociateProductService;

  @PreAuthorize("hasAnyAuthority('master-catalog:read:potentially-associate-products')")
  @GetMapping("/search")
  public CustomPage<MasterCatalogPotentiallyAssociateProductDto> search(
      @RequestParam(value = "taskId") UUID taskId,
      @RequestParam(value = "status", required = false) PotentiallyAssociateProductStatus status,
      @Min(1) @RequestParam(value = "page") Integer page,
      @Min(1) @RequestParam(value = "pageSize") Integer pageSize) {
    PageRequest pageRequest = PageRequest.of(page - 1, pageSize, Sort.by(Order.desc("createdAt")));
    Page<MasterCatalogPotentiallyAssociateProductDto> masterCatalogBatchJobList = potentiallyAssociateProductService.search(taskId,
        status, pageRequest);
    return new CustomPage<MasterCatalogPotentiallyAssociateProductDto>().build(masterCatalogBatchJobList);
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:write:potentially-associate-products')")
  @PutMapping("/{id}")
  public MasterCatalogPotentiallyAssociateProductDto update(
      @PathVariable(value = "id") UUID id,
      @RequestParam("associated") boolean associated) {
    return potentiallyAssociateProductService.update(id, associated);
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:write:potentially-associate-products')")
  @PostMapping("/submit")
  public void submit(@RequestBody List<UUID> potentiallyAssociateProductIds) {
    potentiallyAssociateProductService.submit(potentiallyAssociateProductIds);
  }
}
