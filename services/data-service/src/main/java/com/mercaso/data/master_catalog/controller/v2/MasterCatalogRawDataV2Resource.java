package com.mercaso.data.master_catalog.controller.v2;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogV2Request;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/master-catalog/v2/raw-data")
public class MasterCatalogRawDataV2Resource {

  private final MasterCatalogRawDataService masterCatalogRawDataService;

  @PreAuthorize("hasAnyAuthority('master-catalog:read:raw-data')")
  @GetMapping("/search")
  public CustomPage<MasterCatalogRawDataDto> searchMasterCatalogRawData(
      @Valid SearchMasterCatalogV2Request searchRequest) {

    return new CustomPage<MasterCatalogRawDataDto>().build(masterCatalogRawDataService.searchMasterCatalogRawDataV2(
            searchRequest));
  }
}
