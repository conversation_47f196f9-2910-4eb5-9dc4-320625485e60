package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationRequestDto;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.mapper.MasterCatalogSquareAuthorizationMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import com.mercaso.data.master_catalog.service.MasterCatalogSquareAuthorizationService;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MasterCatalogSquareAuthorizationServiceImpl implements MasterCatalogSquareAuthorizationService {

    private final MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository;

    private final CipherUtility cipherUtility;

    private final MasterCatalogSquareAuthorizationMapper masterCatalogSquareAuthorizationMapper;

    private final SquareApiAdapter squareApiAdapter;

    // Token expiration time in seconds (24 hours)
    private final long expiresIn = 86400;

    @Override
    public MasterCatalogSquareAuthorizationDto createSquareAuthorization(
      MasterCatalogSquareAuthorizationRequestDto masterCatalogSquareAuthorizationRequestDto) {
        MasterCatalogSquareAuthorization masterCatalogSquareAuthorization = new MasterCatalogSquareAuthorization();
        masterCatalogSquareAuthorization.setStoreId(masterCatalogSquareAuthorizationRequestDto.getStoreId());
        masterCatalogSquareAuthorization.setState(masterCatalogSquareAuthorizationRequestDto.getState());
        masterCatalogSquareAuthorization.setEncryptedApplicationId(
          cipherUtility.encrypt(masterCatalogSquareAuthorizationRequestDto.getApplicationId()));
        masterCatalogSquareAuthorization.setEncryptedApplicationSecret(
          cipherUtility.encrypt(masterCatalogSquareAuthorizationRequestDto.getApplicationSecret()));
        masterCatalogSquareAuthorization.setPermissions(
          String.join(",", masterCatalogSquareAuthorizationRequestDto.getPermissions()));

        if (StringUtils.isNotBlank(masterCatalogSquareAuthorizationRequestDto.getAccessToken())) {
            masterCatalogSquareAuthorization.setEncryptedAccessToken(
              cipherUtility.encrypt(masterCatalogSquareAuthorizationRequestDto.getAccessToken()));
            masterCatalogSquareAuthorization.setEncryptedRefreshToken(cipherUtility.encrypt(
              masterCatalogSquareAuthorizationRequestDto.getRefreshToken()));
            masterCatalogSquareAuthorization.setAccessTokenExpiresAt(
              masterCatalogSquareAuthorizationRequestDto.getAccessTokenExpiresAt());
        }

        return masterCatalogSquareAuthorizationMapper.toDto(
          masterCatalogSquareAuthorizationRepository.save(masterCatalogSquareAuthorization));
    }

    @Override
    public MasterCatalogSquareAuthorizationDto getSquareAuthorization(@NotNull UUID storeId) {
        MasterCatalogSquareAuthorization masterCatalogSquareAuthorization = masterCatalogSquareAuthorizationRepository.findByStoreId(
          storeId);

        if (masterCatalogSquareAuthorization == null) {
            return null;
        }

        return masterCatalogSquareAuthorizationMapper.toDto(masterCatalogSquareAuthorization);
    }

    @Override
    public void refreshSquareAuthorization() {
        log.info("Refreshing Square authorizations");

        masterCatalogSquareAuthorizationRepository.findAll().stream()
          .filter(authorization -> StringUtils.isNotBlank(authorization.getEncryptedRefreshToken()))
          .filter(authorization -> StringUtils.isNotBlank(authorization.getEncryptedApplicationId()))
          .filter(authorization -> StringUtils.isNotBlank(authorization.getEncryptedApplicationSecret()))
          .filter(authorization -> isAccessTokenUnset(authorization) || isTokenAboutToExpire(authorization))
          .forEach(this::refreshAuthorizationToken);
    }

    private boolean isAccessTokenUnset(MasterCatalogSquareAuthorization authorization) {
        return authorization.getAccessTokenExpiresAt() == null || StringUtils.isBlank(authorization.getEncryptedAccessToken());
    }

    private boolean isTokenAboutToExpire(MasterCatalogSquareAuthorization authorization) {
        Instant now = Instant.now();
        Instant expiration = authorization.getAccessTokenExpiresAt();
        return now.plusSeconds(expiresIn).isAfter(expiration);
    }

    private void refreshAuthorizationToken(MasterCatalogSquareAuthorization authorization) {
        try {
            SquareObtainTokenResponse response = squareApiAdapter.obtainTokenByRefreshToken(
              cipherUtility.decrypt(authorization.getEncryptedRefreshToken()),
              cipherUtility.decrypt(authorization.getEncryptedApplicationId()),
              cipherUtility.decrypt(authorization.getEncryptedApplicationSecret())
            );

            authorization.setEncryptedAccessToken(cipherUtility.encrypt(response.accessToken()));
            authorization.setAccessTokenExpiresAt(Instant.parse(response.expiresAt()));

            masterCatalogSquareAuthorizationRepository.save(authorization);
            log.info("Successfully refreshed token for storeId: {}", authorization.getStoreId());
        } catch (Exception e) {
            log.error("Failed to refresh token for storeId: {}", authorization.getStoreId(), e);
        }
    }
}
