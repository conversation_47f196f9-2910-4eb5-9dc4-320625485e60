package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsRequestDto;
import com.squareup.square.models.SearchCatalogObjectsRequest;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface SquareSearchCatalogObjectsRequestMapper {

    SearchCatalogObjectsRequest toExternalDto(SearchCatalogObjectsRequestDto dto);

    SearchCatalogObjectsRequestDto fromExternalDto(SearchCatalogObjectsRequest entity);

}
