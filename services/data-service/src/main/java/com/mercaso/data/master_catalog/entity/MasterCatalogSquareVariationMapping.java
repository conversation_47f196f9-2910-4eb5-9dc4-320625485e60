package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_square_variation_mapping")
@AttributeOverrides({
    @AttributeOverride(name = "createdAt", column = @Column(name = "created_at", nullable = false)),
    @AttributeOverride(name = "updatedAt", column = @Column(name = "updated_at", nullable = false))
})
public class MasterCatalogSquareVariationMapping extends BaseEntity {

    private static final long serialVersionUID = 3400850894291244263L;
    @Size(max = 255)
    @NotNull
    @Column(name = "variation_id", nullable = false)
    private String variationId;

    @NotNull
    @Column(name = "master_catalog_raw_data_id", nullable = false)
    private UUID masterCatalogRawDataId;

}