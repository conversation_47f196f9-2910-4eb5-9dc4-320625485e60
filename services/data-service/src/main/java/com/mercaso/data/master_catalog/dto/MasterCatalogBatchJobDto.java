package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link MasterCatalogBatchJob}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogBatchJobDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private String jobNumber;
    private MasterCatalogTaskType taskType;
    private Integer duplicationInBatchTaskCount;
    private Integer duplicationWithProductTaskCount;
    private Integer pendingTaskCount;
    private Integer assignedTaskCount;
    private Integer inProcessTaskCount;
    private Integer completedTaskCount;
    private MasterCatalogBatchJobStatus status;
    private Instant completedAt;
}