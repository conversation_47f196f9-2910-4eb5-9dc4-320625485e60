package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.CreateAssociationInProcessPayload;

@EventPublishConfig(
    publishLocal = true
)
public class CreateAssociationInProcessEvent extends
    BaseApplicationEvent<CreateAssociationInProcessPayload> {

  public CreateAssociationInProcessEvent(Object source, CreateAssociationInProcessPayload payload) {
    super(source, payload);
  }
}
