package com.mercaso.data.master_catalog.event.applicationevent;

import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventTypeProvider;
import com.mercaso.data.master_catalog.event.applicationevent.listener.GenerateProductsSubmitEventListener;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsCompletedEvent;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsInProgressEvent;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsSubmitEvent;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationCompletedEvent;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationInProgressEvent;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationStartEvent;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationSubmitEvent;
import com.mercaso.data.master_catalog.event.model.domain.MasterCatalogRawDataUpdatedEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsCompletedPayload;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsInProgressPayload;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsSubmitPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationInProgressPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationStartPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationSubmitPayload;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataUpdatedPayload;
import com.mercaso.data.master_catalog.event.model.domain.MasterCatalogRawDataSyncedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataSyncedPayload;

public enum BusinessApplicationEventType implements ApplicationEventTypeProvider {

    REMOVE_DUPLICATION_START(
        RemoveDuplicationStartPayload.class,
        RemoveDuplicationStartEvent.class
    ),
    REMOVE_DUPLICATION_IN_PROGRESS(
        RemoveDuplicationInProgressPayload.class,
        RemoveDuplicationInProgressEvent.class
    ),
    /**
     * Listener:{@link
     * com.mercaso.data.master_catalog.event.applicationevent.listener.RemoveDuplicationSubmitEventListener#handleEvent(com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationSubmitEvent)}
     */
    REMOVE_DUPLICATION_SUBMIT(
        RemoveDuplicationSubmitPayload.class,
        RemoveDuplicationSubmitEvent.class
    ),
    REMOVE_DUPLICATION_COMPLETED(
        RemoveDuplicationCompletedPayload.class,
        RemoveDuplicationCompletedEvent.class
    ),
    /**
     * Publisher:
     * {@link
     * com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider#generateProductsSubmitEvent(MasterCatalogBatchJob,
     * java.util.List)}
     *
     * Listener:{@link
     * GenerateProductsSubmitEventListener#handleEvent(com.mercaso.data.master_catalog.event.model.domain.GenerateProductsSubmitEvent)}
     */
    GENERATE_PRODUCTS_SUBMIT(
        GenerateProductsSubmitPayload.class,
        GenerateProductsSubmitEvent.class
    ),
    GENERATE_PRODUCTS_IN_PROGRESS(
        GenerateProductsInProgressPayload.class,
        GenerateProductsInProgressEvent.class
    ),
    GENERATE_PRODUCTS_COMPLETED(
        GenerateProductsCompletedPayload.class,
        GenerateProductsCompletedEvent.class
    ),
    MASTER_CATALOG_RAW_DATA_UPDATED(
        MasterCatalogRawDataUpdatedPayload.class,
        MasterCatalogRawDataUpdatedEvent.class
    ),
    MASTER_CATALOG_RAW_DATA_SYNCED(
        MasterCatalogRawDataSyncedPayload.class,
        MasterCatalogRawDataSyncedEvent.class
    ),
    ;

    // More event types can be added...

    private final Class<? extends BusinessEventPayload<?>> payloadClass;
    private final Class<? extends BaseApplicationEvent<?>> applicationEventClass;

    <P extends BusinessEventPayload<?>> BusinessApplicationEventType(
        Class<P> payloadClass,
        Class<? extends BaseApplicationEvent<P>> applicationEventClass) {
        this.payloadClass = payloadClass;
        this.applicationEventClass = applicationEventClass;
    }

    @Override
    public String getEventTypeName() {
        return this.name().toLowerCase();
    }

    @SuppressWarnings("unchecked")
    public <P extends BusinessEventPayload<?>> Class<P> getPayloadClass() {
        return (Class<P>) payloadClass;
    }

    @SuppressWarnings("unchecked")
    public <P extends BusinessEventPayload<?>> Class<? extends BaseApplicationEvent<P>> getApplicationEventClass() {
        return (Class<? extends BaseApplicationEvent<P>>) applicationEventClass;
    }
} 