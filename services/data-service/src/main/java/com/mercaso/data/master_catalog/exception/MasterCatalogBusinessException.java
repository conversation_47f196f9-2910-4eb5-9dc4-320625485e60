package com.mercaso.data.master_catalog.exception;

import static com.mercaso.data.master_catalog.exception.ErrorCodeEnums.COMMON_CODE;

import lombok.Getter;


@Getter
public class MasterCatalogBusinessException extends RuntimeException {

  private final String code;

  private final Object[] args;

  public MasterCatalogBusinessException(String code) {
    this.code = code;
    this.args = null;
  }

  public MasterCatalogBusinessException(String message, Object... args) {
    super(String.format(message, args));
    this.code = COMMON_CODE.getCode();
    this.args = args;
  }

  public MasterCatalogBusinessException(ErrorCodeEnums codeEnums, Object... args) {
    super(String.format(codeEnums.getCode(), args));
    this.code = codeEnums.getCode();
    this.args = args;
  }

}
