package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogProductAssociationDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogProductAssociationMapper extends
    BaseDoMapper<MasterCatalogProductAssociationDto, MasterCatalogProductAssociation> {

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    MasterCatalogProductAssociation partialUpdate(
        MasterCatalogProductAssociationDto masterCatalogProductAssociationDto,
        @MappingTarget MasterCatalogProductAssociation masterCatalogProductAssociation);
}
