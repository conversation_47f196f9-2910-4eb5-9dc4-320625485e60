package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogUpcPool;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MasterCatalogUpcPoolRepository extends JpaRepository<MasterCatalogUpcPool, String> {

    @Query(value = "SELECT upcs.item FROM UNNEST(string_to_array(:upcs, ',')) as upcs(item) " +
           "WHERE upcs.item NOT IN (SELECT upc FROM master_catalog_upc_pool)",
           nativeQuery = true)
    List<String> findAllUpcsNotIn(@Param("upcs") String upcs);
}
