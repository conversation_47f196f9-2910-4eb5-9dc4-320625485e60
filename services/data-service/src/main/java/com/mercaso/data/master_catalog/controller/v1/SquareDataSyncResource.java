package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.service.SquareDataSyncService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("master-catalog/v1/square-data-sync")
public class SquareDataSyncResource {

    private final SquareDataSyncService squareDataSyncService;

    @PreAuthorize("hasAnyAuthority('master-catalog:write:square-data-sync')")
    @PostMapping("/all")
    public ResponseEntity<Void> syncSquareData(@RequestBody(required = false) SquareDataSyncRequest request) {

        squareDataSyncService.syncSquareData(request);

        return ResponseEntity.ok().build();
    }
}
