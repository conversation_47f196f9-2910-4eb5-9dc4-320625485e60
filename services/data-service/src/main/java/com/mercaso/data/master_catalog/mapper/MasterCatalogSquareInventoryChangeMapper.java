package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareInventoryChangeDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogSquareInventoryChangeMapper extends
    BaseDoMapper<MasterCatalogSquareInventoryChangeDto, MasterCatalogSquareInventoryChange> {

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    MasterCatalogSquareInventoryChange partialUpdate(
        MasterCatalogSquareInventoryChangeDto masterCatalogSquareInventoryChangeDto,
        @MappingTarget MasterCatalogSquareInventoryChange masterCatalogSquareInventoryChange);
}