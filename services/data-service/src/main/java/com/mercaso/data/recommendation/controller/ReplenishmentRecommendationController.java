package com.mercaso.data.recommendation.controller;

import static com.mercaso.data.master_catalog.constants.SquareConstants.REPLENISHMENT_STORE_TO_CUSTOM;

import com.mercaso.data.master_catalog.dto.ReplenishmentForecastFileExportResult;
import com.mercaso.data.master_catalog.dto.ReplenishmentForecastRequestDto;
import com.mercaso.data.recommendation.service.impl.ReplenishmentRecommendationService;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.UUID;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/replenishment-forecast")
@RequiredArgsConstructor
@Validated
public class ReplenishmentRecommendationController {

    private final ReplenishmentRecommendationService replenishmentRecommendationService;

    @GetMapping("/export")
    @PreAuthorize("hasAnyAuthority('master-catalog:read:replenishment-forecast')")
    public ResponseEntity<byte[]> exportForecast(@RequestParam("storeId") @Valid @NonNull String storeId) {

        // Hardcoded storeId check
        if (!REPLENISHMENT_STORE_TO_CUSTOM.containsKey(storeId)) {
            return ResponseEntity.badRequest().build();
        }

        // Convert storeId to UUID and export the forecast data
        ReplenishmentForecastFileExportResult exportResult = replenishmentRecommendationService
            .exportDataToCSV(UUID.fromString(storeId));

        // Build the response headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setAccessControlExposeHeaders(Collections.singletonList("Content-Disposition"));
        headers.setContentDisposition(ContentDisposition.attachment()
            .filename(exportResult.getFileName())
            .build());

        // Return the response with the file content
        return ResponseEntity.ok()
            .headers(headers)
            .body(exportResult.getContent());
    }

    @PostMapping("/do-forecast")
    @PreAuthorize("hasAnyAuthority('master-catalog:write:replenishment-forecast')")
    public ResponseEntity<Void> doForecast(
        @RequestBody(required = false) ReplenishmentForecastRequestDto replenishmentForecastRequestDto) {
        replenishmentRecommendationService.generateReplenishment(
            replenishmentForecastRequestDto != null ? replenishmentForecastRequestDto.getStoreIds() : null);
        return ResponseEntity.status(HttpStatus.OK).build();
    }
}