package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import com.mercaso.data.master_catalog.dto.external.UploadDocumentRequestDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogImageService;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RequiredArgsConstructor
@Service
public class MasterCatalogImageServiceImpl implements MasterCatalogImageService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    private final S3OperationAdapter s3OperationAdapter;
    private final MasterCatalogImageRepository masterCatalogImageRepository;

    @Override
    public DocumentResponseDto uploadImage(MultipartFile file) {
        log.info("Starting image upload to S3");

        // Validate file
        validateFile(file);

        try {
            // Generate unique filename to prevent overwrites
            String uniqueFilename = generateUniqueFilename(file.getOriginalFilename());
            log.debug("Generated unique filename: {}", uniqueFilename);

            // Upload to S3
            UploadDocumentRequestDto uploadRequest = UploadDocumentRequestDto.builder()
                .documentName(uniqueFilename)
                .content(file.getBytes())
                .build();

            DocumentResponseDto uploadResponse = s3OperationAdapter.upload(uploadRequest);
            log.info("Successfully uploaded image to S3 with name: {}", uploadResponse.getName());

            return uploadResponse;

        } catch (IOException e) {
            log.error("Failed to read file bytes for upload", e);
            throw new MasterCatalogBusinessException(ErrorCodeEnums.MC_UPLOAD_IMAGE_FAILURE);
        } catch (Exception e) {
            log.error("Failed to upload image", e);
            throw new MasterCatalogBusinessException(ErrorCodeEnums.MC_UPLOAD_IMAGE_FAILURE);
        }
    }

    /**
     * Validates the uploaded file.
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.error("File is null or empty");
            throw new MasterCatalogBusinessException(ErrorCodeEnums.MC_UPLOAD_IMAGE_EMPTY);
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            log.error("File has no name");
            throw new MasterCatalogBusinessException(ErrorCodeEnums.MC_UPLOAD_IMAGE_INVALID_NAME);
        }

        // Validate file is an image by checking content type
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            log.error("File is not an image. Content type: {}", contentType);
            throw new MasterCatalogBusinessException(ErrorCodeEnums.MC_UPLOAD_IMAGE_INVALID_TYPE);
        }
    }

    /**
     * Generates a unique filename based on the original filename to prevent overwrites.
     * Format: originalName_yyyyMMddHHmmssSSS_randomUUID.extension
     */
    private String generateUniqueFilename(String originalFilename) {
        String baseName = FilenameUtils.getBaseName(originalFilename);
        String extension = FilenameUtils.getExtension(originalFilename);
        
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        String uniqueId = UUID.randomUUID().toString().substring(0, 8); // Use first 8 chars of UUID
        
        return String.format("%s_%s_%s.%s", baseName, timestamp, uniqueId, extension);
    }

    @Override
    public Map<UUID, List<String>> getImagesPathByRawDataIds(List<UUID> rawDataIds) {

        List<MasterCatalogImage> images = masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(rawDataIds);

        Map<UUID, List<String>> map = new HashMap<>();
        for (MasterCatalogImage image : images) {
            String imagePath = image.getImagePath();
            if (!imagePath.startsWith("https://") && !imagePath.startsWith("http://")) {
                image.setImagePath(s3OperationAdapter.getSignedUrl(imagePath));
            }
            String path = image.getImagePath();
            map.computeIfAbsent(image.getMasterCatalogRawDataId(), k -> new ArrayList<>()).add(path);
        }
        return map;
    }

}
