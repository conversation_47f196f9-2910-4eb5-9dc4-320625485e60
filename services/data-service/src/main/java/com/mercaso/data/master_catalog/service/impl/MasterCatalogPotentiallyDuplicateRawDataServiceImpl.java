package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.constants.CommonConstants.ADMIN_ROLE;

import com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateRawDataRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateSubmitRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyDuplicateRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogImageService;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyDuplicateRawDataService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import jakarta.persistence.criteria.Predicate;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.ObjectNotFoundException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Setter
@RequiredArgsConstructor
public class MasterCatalogPotentiallyDuplicateRawDataServiceImpl implements
    MasterCatalogPotentiallyDuplicateRawDataService {
  @Value("${master-catalog.task-records:20}")
  private int TASK_RECORD_COUNT;

  private final MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository;
  private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
  private final MasterCatalogTaskService masterCatalogTaskService;
  private final MasterCatalogTaskRepository masterCatalogTaskRepository;
  private final MasterCatalogPotentiallyDuplicateRawDataMapper masterCatalogPotentiallyDuplicateRawDataMapper;
  private final MasterCatalogRawDataService masterCatalogRawDataService;
  private final ApplicationEventPublisherProvider applicationEventPublisherProvider;
  private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
  private final MasterCatalogImageService masterCatalogImageService;

  @Override
  public Page<MasterCatalogPotentiallyDuplicateRawDataDto> list(PotentiallyDuplicateRawDataRequest request) {
    PageRequest pageRequest = PageRequest.of(request.getPage() - 1, request.getPageSize(), Sort.by(Order.desc("createdAt")));

    Specification<MasterCatalogPotentiallyDuplicateRawData> specification = (root, query, builder) -> {
      List<Predicate> predicates = new ArrayList<>();

      if (StringUtils.isNotBlank(request.getStatus())){
        predicates.add(builder.equal(root.get("status"), PotentiallyDuplicateRawDataStatus.valueOf(request.getStatus())));
      }
      predicates.add(builder.equal(root.get("taskId"), UUID.fromString(request.getTaskId())));

      return builder.and(predicates.toArray(new Predicate[0]));
    };

    Page<MasterCatalogPotentiallyDuplicateRawDataDto> masterCatalogPotentiallyDuplicateRawDataDtos = masterCatalogPotentiallyDuplicateRawDataRepository.findAll(
        specification, pageRequest).map(masterCatalogPotentiallyDuplicateRawDataMapper::toDto);


    List<UUID> rawDataIds = new ArrayList<>(masterCatalogPotentiallyDuplicateRawDataDtos.getContent()
        .stream()
        .map(MasterCatalogPotentiallyDuplicateRawDataDto::getPotentiallyDuplicateRawDataId)
        .toList());

    rawDataIds.addAll(masterCatalogPotentiallyDuplicateRawDataDtos.getContent()
        .stream()
        .map(MasterCatalogPotentiallyDuplicateRawDataDto::getRawDataId)
        .toList());

    Map<UUID, List<String>> imagesPathByRawDataIds = masterCatalogImageService.getImagesPathByRawDataIds(
        rawDataIds.stream().distinct().toList());

    masterCatalogPotentiallyDuplicateRawDataDtos.forEach(masterCatalogPotentiallyDuplicateRawDataDto -> {
      if (masterCatalogPotentiallyDuplicateRawDataDto.getRawDataId() != null) {
        masterCatalogPotentiallyDuplicateRawDataDto.setImages(imagesPathByRawDataIds.get(
            masterCatalogPotentiallyDuplicateRawDataDto.getRawDataId()));
      }

      if (masterCatalogPotentiallyDuplicateRawDataDto.getPotentiallyDuplicateRawDataId() != null) {
        masterCatalogPotentiallyDuplicateRawDataDto.setPotentiallyDuplicateImages(imagesPathByRawDataIds.get(
            masterCatalogPotentiallyDuplicateRawDataDto.getPotentiallyDuplicateRawDataId()));
      }
    });

    return masterCatalogPotentiallyDuplicateRawDataDtos;
  }

  @Transactional
  @Override
  public void submit(PotentiallyDuplicateSubmitRequest potentiallyDuplicateSubmitRequest) {
    List<UUID> potentiallyDuplicateRawDataIds = potentiallyDuplicateSubmitRequest.getPotentiallyDuplicateRawDataIds();
    log.info("Submitting potentially duplicate raw data with ids: {}", potentiallyDuplicateRawDataIds);

    // Fetch data to be submitted
    List<MasterCatalogPotentiallyDuplicateRawData> waitingSubmit = masterCatalogPotentiallyDuplicateRawDataRepository
        .findAllById(potentiallyDuplicateRawDataIds);
    log.info("Submitting potentially duplicate, found {} potentially duplicate raw data", waitingSubmit.size());

    if (CollectionUtils.isEmpty(waitingSubmit)) {
      log.info("Submitting potentially duplicate, no data to submit");
      return;
    }

    Set<UUID> taskIdSet = waitingSubmit.stream()
        .map(MasterCatalogPotentiallyDuplicateRawData::getTaskId)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());

    if (taskIdSet.size() != 1) {
      log.warn("no or multiple duplication raw data not belong to the same task {}", taskIdSet);
      throw new IllegalArgumentException("no or multiple duplication raw data not belong to the same task");
    }

    //check if user has enough permission to access the data
    masterCatalogTaskService.validateUserPermission(taskIdSet.iterator().next(), "submit");

    // Filter data with IN_STAGE status
    List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit = filterValidToSubmitData(waitingSubmit);
    log.info("Submitting potentially duplicate, found {} valid data to submit", validToSubmit.size());

    if (CollectionUtils.isEmpty(validToSubmit)) {
      log.info("Submitting potentially duplicate, no valid data to submit because of the status is not in stage.");
      return;
    }

    // Get job ID and process
    validToSubmit.stream()
        .map(MasterCatalogPotentiallyDuplicateRawData::getJobId)
        .findFirst()
        .flatMap(this::getAndValidateJob)
        .ifPresent(job -> processSubmitByJobStatus(job, validToSubmit));
  }

  private List<MasterCatalogPotentiallyDuplicateRawData> filterValidToSubmitData(
      List<MasterCatalogPotentiallyDuplicateRawData> waitingSubmit) {

    return waitingSubmit.stream()
        .filter(data -> data.getStatus() == PotentiallyDuplicateRawDataStatus.IN_STAGE)
        .toList();
  }

  /**
   * Get and validate job
   */
  private Optional<MasterCatalogBatchJob> getAndValidateJob(UUID jobId) {
    return masterCatalogBatchJobRepository.findById(jobId)
        .map(job -> {
          log.info("Submitting potentially duplicate, job {} status is {}", job.getId(), job.getStatus());
          if (!MasterCatalogBatchJobConstants.AVAILABLE_SUBMIT_JOB_STATUSES.contains(job.getStatus())) {
            throw new IllegalArgumentException(
                "Submitting potentially duplicate, job status is not in available submit job status, please confirm the data.");
          }
          return job;
        })
        .or(() -> {
          log.info("Submitting potentially duplicate, job {} not found", jobId);
          throw new IllegalArgumentException(
              "Submitting potentially duplicate, job not found, please confirm the data.");
        });
  }

  /**
   * Process submission based on job status
   */
  private void processSubmitByJobStatus(MasterCatalogBatchJob job,
      List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit) {

    Map<MasterCatalogBatchJobStatus, StatusHandler> statusHandlers = buildProductGenerationJobStatusHandlerMap();

    // Get the corresponding processor
    StatusHandler handler = statusHandlers.get(job.getStatus());

    validToSubmit.forEach(data -> data.setStatus(handler.targetStatus()));
    masterCatalogPotentiallyDuplicateRawDataRepository.saveAll(validToSubmit);

    markMasterCatalogRawDataAsCompleted(validToSubmit);

    handler.eventPublisher().accept(job, validToSubmit);
  }

  private Map<MasterCatalogBatchJobStatus, StatusHandler> buildProductGenerationJobStatusHandlerMap() {

    return Map.of(
        MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS,
        new StatusHandler(
            PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED,
            applicationEventPublisherProvider::removeDuplicationSubmitEvent
        ),
        MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_IN_PROGRESS,
        new StatusHandler(
            PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED,
            applicationEventPublisherProvider::generateProductsSubmitEvent
        )
    );
  }

  // Define the mapping of job statuses and their corresponding handling
  private record StatusHandler(
      PotentiallyDuplicateRawDataStatus targetStatus,
      BiConsumer<MasterCatalogBatchJob, List<MasterCatalogPotentiallyDuplicateRawData>> eventPublisher
  ) {

  }

  private void markMasterCatalogRawDataAsCompleted(List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit) {
    Set<UUID> potentiallyDuplicateRawDataIds = validToSubmit.stream()
        .filter(data -> Boolean.TRUE.equals(data.getDuplicated()))
        .map(MasterCatalogPotentiallyDuplicateRawData::getPotentiallyDuplicateRawDataId)
        .collect(Collectors.toSet());
    masterCatalogRawDataService.markAsCompleted(potentiallyDuplicateRawDataIds);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public MasterCatalogPotentiallyDuplicateRawDataDto update(UUID id,
      MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request) {

    // Find data and verify existence
    MasterCatalogPotentiallyDuplicateRawData potentiallyDuplicateRawData = masterCatalogPotentiallyDuplicateRawDataRepository
        .findById(id)
        .orElseThrow(() -> new ObjectNotFoundException(id, MasterCatalogPotentiallyDuplicateRawData.class.getName()));

    //check if user has enough permission to access this data
    MasterCatalogTask task = masterCatalogTaskService.validateUserPermission(
        potentiallyDuplicateRawData.getTaskId(), "review");

    // Validate data status
    validateDataStatus(potentiallyDuplicateRawData);
    // Validate job status
    validateJobStatus(potentiallyDuplicateRawData.getJobId());

    // Update data
    if (request.getDuplicated() != null) {
      if (MasterCatalogTaskStatus.ASSIGNED == task.getStatus()) {
        task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);
        masterCatalogTaskRepository.save(task);
      }

      potentiallyDuplicateRawData.setDuplicated(request.getDuplicated());
      potentiallyDuplicateRawData.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
      // Save and return result
      MasterCatalogPotentiallyDuplicateRawData saved = masterCatalogPotentiallyDuplicateRawDataRepository.save(
          potentiallyDuplicateRawData);

      return masterCatalogPotentiallyDuplicateRawDataMapper.toDto(saved);
    }

    return masterCatalogPotentiallyDuplicateRawDataMapper.toDto(potentiallyDuplicateRawData);
  }

  // Validate data status
  private void validateDataStatus(MasterCatalogPotentiallyDuplicateRawData data) {
    if (data.getStatus() == PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED) {
      throw new IllegalArgumentException("The data is already reviewed, cannot update.");
    }
  }

  // Validate job status
  private void validateJobStatus(UUID jobId) {
    masterCatalogBatchJobRepository.findById(jobId)
        .filter(job -> MasterCatalogBatchJobConstants.AVAILABLE_SUBMIT_JOB_STATUSES.contains(job.getStatus()))
        .orElseThrow(
            () -> new IllegalArgumentException("The job is not in available submit job status, cannot update."));
  }

  private Page<MasterCatalogPotentiallyDuplicateRawDataDto> findPendingDuplicates(
      PotentiallyDuplicateRawDataRequest request) {

    PageRequest pageRequest = PageRequest.of(request.getPage() - 1, request.getPageSize(),
        Sort.by(Direction.DESC, "createdAt"));

    List<PotentiallyDuplicateRawDataStatus> status =
        StringUtils.isEmpty(request.getStatus()) ? MasterCatalogBatchJobConstants.PENDING_STATUSES
            : List.of(PotentiallyDuplicateRawDataStatus.valueOf(request.getStatus()));

    return masterCatalogPotentiallyDuplicateRawDataRepository.findByStatusIsIn(status, pageRequest)
        .map(masterCatalogPotentiallyDuplicateRawDataMapper::toDto);
  }

  public List<MasterCatalogPotentiallyDuplicateRawDataDto> processPotentiallyDuplicateItem(List<List<UUID>> rawDataIds,
      UUID jobId,
      PotentiallyDuplicateRawDataStatus status, MasterCatalogTaskType type) {

    //Separating to two list, duplicateUUIDList contains all the potentially duplicated pair, unduplicateUUIDList contains those non duplicated UUID that do not need human review.
    List<List<UUID>> duplicateUUIDList = rawDataIds.stream()
        .filter(list -> list.size() == 2)
        .toList();

    List<UUID> unduplicateUUIDList = rawDataIds.stream()
        .filter(list -> list.size() == 1)
        .flatMap(List::stream)
        .toList();

    // Retrieve raw data accordingly and convert raw data to potentiallyDuplicateRawData
    List<MasterCatalogPotentiallyDuplicateRawData> pendingSaveDuplicateRawDataList = new ArrayList<>(
        generateDuplicateRawData(
            unduplicateUUIDList, jobId, status));

    int taskCount = (duplicateUUIDList.size() + TASK_RECORD_COUNT - 1) / TASK_RECORD_COUNT;
    List<MasterCatalogTaskDto> tasks = masterCatalogTaskService.createTasks(jobId, type, taskCount);
    List<UUID> taskIds = tasks.stream().map(MasterCatalogTaskDto::getId).toList();

    int index = 0;
    for (int i = 0; i < taskCount; i++) {
      UUID taskId = taskIds.get(i);
      int endIndex = Math.min(index + TASK_RECORD_COUNT, duplicateUUIDList.size());
      List<List<UUID>> subList = duplicateUUIDList.subList(index, endIndex);

      for (List<UUID> duplicateGroup : subList) {
        MasterCatalogPotentiallyDuplicateRawData entity =
            buildPotentiallyDuplicateRawData(duplicateGroup, jobId, taskId);
        if (entity != null) {
          pendingSaveDuplicateRawDataList.add(entity);
        }
      }
      index = endIndex;

      if (index >= duplicateUUIDList.size()) {
        break;
      }
    }

    masterCatalogPotentiallyDuplicateRawDataRepository.saveAll(pendingSaveDuplicateRawDataList);
    return pendingSaveDuplicateRawDataList.stream()
        .map(masterCatalogPotentiallyDuplicateRawDataMapper::toDto)
        .toList();
  }

  private List<MasterCatalogPotentiallyDuplicateRawData> generateDuplicateRawData(
      List<UUID> unduplicatedUUIDList, UUID jobId, PotentiallyDuplicateRawDataStatus status) {
    //Retrieve raw data
    List<MasterCatalogRawData> rawDataList = masterCatalogRawDataRepository.findAllById(
        unduplicatedUUIDList);

    //Mapping raw data to MasterCatalogPotentiallyDuplicateRawData
    List<MasterCatalogPotentiallyDuplicateRawData> masterCatalogPotentiallyDuplicateRawDataList = rawDataList.stream()
        .map(
            rawData -> masterCatalogPotentiallyDuplicateRawDataMapper.mapRawDataToPotentiallyUnDuplicateRawData(
                UUID.randomUUID(),
                rawData, jobId, status)).toList();
    //Setting time
    masterCatalogPotentiallyDuplicateRawDataList.stream().filter(Objects::nonNull).forEach(
        masterCatalogPotentiallyDuplicateRawData -> {
          masterCatalogPotentiallyDuplicateRawData.setCreatedAt(
              Instant.now());
          masterCatalogPotentiallyDuplicateRawData.setUpdatedAt(Instant.now());
        });
    return masterCatalogPotentiallyDuplicateRawDataList;
  }

  private MasterCatalogPotentiallyDuplicateRawData buildPotentiallyDuplicateRawData(
      List<UUID> duplicatedUUIDList, UUID jobId, UUID taskId) {

    Optional<MasterCatalogRawData> productOptional = masterCatalogRawDataRepository.findById(
        duplicatedUUIDList.getFirst());
    Optional<MasterCatalogRawData> potentialDuplicateOptional = masterCatalogRawDataRepository.findById(
        duplicatedUUIDList.getLast());

    //if cannot find by id
    if (productOptional.isEmpty()
        || potentialDuplicateOptional.isEmpty()) {
      log.error("Generate Potentially Duplicate Raw Data Error, jobId: {}, for UUIDs: {}",
          jobId, duplicatedUUIDList);
      return null;
    }

    MasterCatalogRawData product = productOptional.get();
    MasterCatalogRawData potentialDuplicateRawData = potentialDuplicateOptional.get();

    //Mapping raw data to MasterCatalogPotentiallyDuplicateRawData
    MasterCatalogPotentiallyDuplicateRawData masterCatalogPotentiallyDuplicateRawData = masterCatalogPotentiallyDuplicateRawDataMapper.mapRawDataToPotentialDuplicateRawData(
        UUID.randomUUID(), jobId, product, potentialDuplicateRawData,
        PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);
    //Setting time
    masterCatalogPotentiallyDuplicateRawData.setTaskId(taskId);
    masterCatalogPotentiallyDuplicateRawData.setCreatedAt(Instant.now());
    masterCatalogPotentiallyDuplicateRawData.setUpdatedAt(Instant.now());
    return masterCatalogPotentiallyDuplicateRawData;
  }
}
