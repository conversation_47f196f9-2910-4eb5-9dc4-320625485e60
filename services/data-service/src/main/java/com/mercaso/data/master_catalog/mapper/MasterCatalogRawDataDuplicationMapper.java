package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDuplicationDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogRawDataDuplicationMapper extends
    BaseDoMapper<MasterCatalogRawDataDuplicationDto, MasterCatalogRawDataDuplication> {

}