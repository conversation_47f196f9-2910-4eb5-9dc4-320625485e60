package com.mercaso.data.master_catalog.adaptor.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.external.SearchUpcsByImageResponse;
import com.mercaso.data.master_catalog.dto.external.UpcByDescriptionDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.utils.HttpClientUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.core5.http.Method;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Component
public class ExternalApiAdapterImpl implements ExternalApiAdapter {

    @SuppressWarnings("all")
    private static final String SEARCH_DESCRIPTION_URL = "http://**************:8883/marqo/product/v1/";
    @SuppressWarnings("all")
    private static final String SEARCH_BY_IMAGE_URL = "http://**************:8883/marqo/image/upload";

    @Override
    public List<String> getUpcsFromExternalApi(String description) {
        try {

            return filterUpcsFrom(HttpClientUtils.executeGetRequest(SEARCH_DESCRIPTION_URL + description, new TypeReference<>() {
            }));
        } catch (IOException e) {
            log.error("Failed to get upcs from external api", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getUpcsFromExternalApiByImage(MultipartFile file) {

        try {

            return filterUpcsFromSearchUpcsByImageResponse(HttpClientUtils.executeRequest(SEARCH_BY_IMAGE_URL,
                Method.POST,
                file,
                new TypeReference<>() {
                }));
        } catch (IOException e) {
            log.error("Failed to get upcs from external api", e);
            return Collections.emptyList();
        }
    }

    @Override
    public void sendInitialProcessing(UUID taskId, List<MasterCatalogRawData> data) {
        log.info("send initial processing data: {}", data);
    }

    @Override
    public void sendFinalProcessing(UUID taskId, List<MasterCatalogPotentiallyDuplicateRawDataDto> data) {
        log.info("send final processing data: {}", data);
    }

    private List<String> filterUpcsFrom(List<UpcByDescriptionDto> response) {

        if (response == null) {
            return Collections.emptyList();
        }
        log.info("response: {}", response);
        return response.stream()
            .map(UpcByDescriptionDto::getUpc)
            .filter(Objects::nonNull)
            .toList();
    }

    private List<String> filterUpcsFromSearchUpcsByImageResponse(SearchUpcsByImageResponse response) {

        if (response == null || CollectionUtils.isEmpty(response.getMessage())) {
            return Collections.emptyList();
        }

        log.info("response data: {}", response.getMessage());
        return response.getMessage().stream()
            .map(UpcByDescriptionDto::getUpc)
            .filter(Objects::nonNull)
            .toList();
    }
}
