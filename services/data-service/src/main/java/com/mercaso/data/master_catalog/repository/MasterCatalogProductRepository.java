package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface MasterCatalogProductRepository extends JpaRepository<MasterCatalogProduct, UUID>,
    JpaSpecificationExecutor<MasterCatalogProduct> {

  List<MasterCatalogProduct> findAllByUpcIn(List<String> upcs);

  List<MasterCatalogProduct> findAllByMasterCatalogRawDataIdIn(Collection<UUID> masterCatalogRawDataIds);

  Optional<MasterCatalogProduct> findByMasterCatalogRawDataId(UUID masterCatalogRawDataId);

  @Query("SELECT p.upc, p.name FROM MasterCatalogProduct p WHERE p.name in(:names)")
  List<ProductUpcAndName> findAllUpcsByName(List<String> names);

  @Modifying
  @Query("update MasterCatalogProduct p set p.associated = true where p.id in (:productIds)")
  void markAsAssociated(Set<UUID> productIds);

  interface ProductUpcAndName {

    String getUpc();

    String getName();
  }

}