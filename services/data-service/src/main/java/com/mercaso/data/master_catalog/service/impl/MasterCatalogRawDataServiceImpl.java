package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogV2Request;
import com.mercaso.data.master_catalog.dto.UpdateMasterCatalogRequest;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.mapper.MasterCatalogRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RequiredArgsConstructor
@Service
public class MasterCatalogRawDataServiceImpl implements MasterCatalogRawDataService {

    private final MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final MasterCatalogRawDataMapper masterCatalogRawDataMapper;
    private final MasterCatalogImageRepository masterCatalogImageRepository;
    private final ExternalApiAdapter externalApiAdapter;
    private final S3OperationAdapter s3OperationAdapter;
  private final ApplicationEventPublisherProvider applicationEventPublisherProvider;

    @Override
    public Page<MasterCatalogRawDataDto> searchMasterCatalogRawData(SearchMasterCatalogRequest searchRequest) {
        PageRequest pageRequest = PageRequest.of(searchRequest.getPage() - 1, searchRequest.getPageSize());

        Page<MasterCatalogRawData> rawData;
        if (StringUtils.isNotBlank(searchRequest.getDescription())) {
            List<String> upcs = externalApiAdapter.getUpcsFromExternalApi(searchRequest.getDescription());
            rawData = masterCatalogRawDataRepository.findByUpcIn(upcs, pageRequest);
        } else {
            rawData = findRawData(searchRequest, pageRequest);
        }

        if (rawData.isEmpty()) {
            return Page.empty();
        }

      Map<UUID, List<String>> imagePathsMap = getImagePathsMap(rawData.getContent());

        return rawData.map(rawDataEntity -> mapToDto(rawDataEntity, imagePathsMap));
    }

    @Override
    public Page<MasterCatalogRawDataDto> searchMasterCatalogRawDataByImage(MultipartFile file, Pageable pageable) {
        List<String> upcs = externalApiAdapter.getUpcsFromExternalApiByImage(file);

        Page<MasterCatalogRawData> rawData = masterCatalogRawDataRepository.findByUpcIn(upcs, pageable);

        if (rawData.isEmpty()) {
            return Page.empty();
        }

      Map<UUID, List<String>> imagePathsMap = getImagePathsMap(rawData.getContent());

        return rawData.map(rawDataEntity -> mapToDto(rawDataEntity, imagePathsMap));
    }

    @Override
    public void markAsCompleted(Collection<UUID> ids) {
        log.info("Marking raw data as completed: {}", ids);
        
        List<MasterCatalogRawData> rawData = masterCatalogRawDataRepository.findAllById(ids);

        rawData.forEach(data -> {
          data.setStatus(RawDataStatus.COMPLETED.name());
          data.setCompletedAt(Instant.now());
        });

        masterCatalogRawDataRepository.saveAll(rawData);
    }

  @Override
  public MasterCatalogRawDataDto findById(UUID id) {
    Optional<MasterCatalogRawData> masterCatalogRawData = masterCatalogRawDataRepository.findById(
        id);
    return masterCatalogRawData.map(rawDataEntity -> {
      Map<UUID, List<String>> imagePathsMap = getImagePathsMap(
          Collections.singletonList(rawDataEntity));
      return mapToDto(rawDataEntity, imagePathsMap);
    }).orElseThrow(() -> {
      log.error("MasterCatalogRawData with id {} not found", id);
      return new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_RAW_DATA_NOT_FOUND);
    });
  }

    @Override
    public Page<MasterCatalogRawDataDto> searchDuplicationRawData(Integer pageNumber,Integer pageSize, String upc) {
        PageRequest pageRequest = PageRequest.of(pageNumber - 1, pageSize);

        List<MasterCatalogRawDataDuplication> duplications = masterCatalogRawDataDuplicationRepository.findByUpc(upc);
        if (duplications.isEmpty()){
            log.info("No duplications found for upc: {}", upc);
            return Page.empty();
        }

        //get duplication upcs from duplication group
        List<UUID> duplicationGroupLIds = duplications
                .stream()
                .map(MasterCatalogRawDataDuplication::getDuplicationGroup)
                .distinct()
                .toList();

        List<String> upcList = masterCatalogRawDataDuplicationRepository.findUpcsByDuplicationGroupIn(duplicationGroupLIds);
        Page<MasterCatalogRawData> rawDataPage = masterCatalogRawDataRepository.findByUpcIn(upcList, pageRequest);

        //get raw data images
        Map<UUID, List<String>> imagePathsMap = getImagePathsMap(rawDataPage.getContent());

        return rawDataPage.map(rawData -> mapToDto(rawData, imagePathsMap));
    }

    private Page<MasterCatalogRawData> findRawData(SearchMasterCatalogRequest searchRequest, PageRequest pageRequest) {
        return switch (searchRequest.getSearchType()) {
            case UPC -> masterCatalogRawDataRepository.findByUpcIs(searchRequest.getUpc(), pageRequest);
            case STORE_ID ->
                masterCatalogRawDataRepository.findByStoreIdIs(UUID.fromString(searchRequest.getStoreId()), pageRequest);
            case ALL -> masterCatalogRawDataRepository.findAll(pageRequest);
        };
    }

  private Map<UUID, List<String>> getImagePathsMap(List<MasterCatalogRawData> rawDataList) {
    List<UUID> rawDataIds = rawDataList.stream()
            .map(MasterCatalogRawData::getId)
            .toList();

        List<MasterCatalogImage> images = masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(rawDataIds);

        return images
            .stream().peek(image -> {
                String imagePath = image.getImagePath();
                if (!imagePath.startsWith("https://") && !imagePath.startsWith("http://")) {
                    image.setImagePath(s3OperationAdapter.getSignedUrl(imagePath));
                }
            })
            .collect(Collectors.groupingBy(
                MasterCatalogImage::getMasterCatalogRawDataId,
                Collectors.mapping(MasterCatalogImage::getImagePath, Collectors.toList())
            ));
    }

    private MasterCatalogRawDataDto mapToDto(MasterCatalogRawData rawDataEntity, Map<UUID, List<String>> imagePathsMap) {
        MasterCatalogRawDataDto dto = masterCatalogRawDataMapper.toDto(rawDataEntity);
        dto.setImages(imagePathsMap.getOrDefault(rawDataEntity.getId(), List.of()));
        return dto;
    }

  @Override
  public Page<MasterCatalogRawDataDto> searchMasterCatalogRawDataV2(
      SearchMasterCatalogV2Request searchRequest) {

    PageRequest pageRequest = PageRequest.of(searchRequest.getPage() - 1, searchRequest.getPageSize());

    Page<MasterCatalogRawData> rawData = masterCatalogRawDataRepository.searchMasterCatalogRawDataV2(
        searchRequest.getStoreId(),
        StringUtils.isNotBlank(searchRequest.getUpc()) ? searchRequest.getUpc() : null,
        StringUtils.isNotBlank(searchRequest.getDepartment()) ? searchRequest.getDepartment() : null,
        StringUtils.isNotBlank(searchRequest.getCategory()) ? searchRequest.getCategory() : null,
        StringUtils.isNotBlank(searchRequest.getSubCategory()) ? searchRequest.getSubCategory() : null,
        StringUtils.isNotBlank(searchRequest.getClazz()) ? searchRequest.getClazz() : null,
        StringUtils.isNotBlank(searchRequest.getDescription()) ? searchRequest.getDescription() : null,
        pageRequest
    );
    
    if (rawData.isEmpty()) {
      return Page.empty();
    }

    Map<UUID, List<String>> imagePathsMap = getImagePathsMap(rawData.getContent());

    return rawData.map(rawDataEntity -> mapToDto(rawDataEntity, imagePathsMap));
  }

  @Override
  @Transactional
  public void updateMasterCatalogRawData(UUID id, UpdateMasterCatalogRequest updateRequest) {
    log.info("Updating MasterCatalogRawData with id: {}", id);

    MasterCatalogRawData originalRawData = masterCatalogRawDataRepository.findById(id)
        .orElseThrow(() -> {
          log.error("MasterCatalogRawData with id {} not found", id);
          return new MasterCatalogBusinessException(
              ErrorCodeEnums.MASTER_CATALOG_RAW_DATA_NOT_FOUND);
        });

    MasterCatalogRawData previousRawData = new MasterCatalogRawData();
    BeanUtils.copyProperties(originalRawData, previousRawData);
    previousRawData.setImages(masterCatalogImageRepository.findByMasterCatalogRawDataId(id));

    // Update the original raw data properties
    originalRawData.setName(updateRequest.getName());
    originalRawData.setDescription(updateRequest.getDescription());
    originalRawData.setBrand(updateRequest.getBrand());
    originalRawData.setPackageSize(updateRequest.getPackageSize());
    PackageType packageType = (updateRequest.getPackageSize() != null && updateRequest.getPackageSize() > 1) 
        ? PackageType.PACK 
        : PackageType.INDIVIDUAL;
    originalRawData.setPackageType(packageType);
    originalRawData.setDepartment(updateRequest.getDepartment());
    originalRawData.setCategory(updateRequest.getCategory());
    originalRawData.setSubCategory(updateRequest.getSubcategory());
    originalRawData.setClazz(updateRequest.getClazz());

    MasterCatalogRawData currentRawData = masterCatalogRawDataRepository.save(originalRawData);

    masterCatalogImageRepository.deleteByMasterCatalogRawDataId(id);

    if (updateRequest.getImages() != null && !updateRequest.getImages().isEmpty()) {
      List<MasterCatalogImage> newImages = updateRequest.getImages().stream()
          .map(imageRequest -> MasterCatalogImage.builder()
              .imagePath(imageRequest.getImagePath())
              .masterCatalogRawDataId(id)
              .primaryImage(imageRequest.isPrimaryImage())
              .build())
          .collect(Collectors.toList());

      List<MasterCatalogImage> masterCatalogImages = masterCatalogImageRepository.saveAll(
          newImages);

      currentRawData.setImages(masterCatalogImages);
    }

    // Publish event
    applicationEventPublisherProvider.publishMasterCatalogRawDataUpdatedEvent(id,
        currentRawData, previousRawData);
    log.info("Published Master Catalog Raw Data Updated event for id: {}", id);
  }

  @Override
  @Transactional
  public void syncMasterCatalogRawData(MasterCatalogRawData sourceData) {
    log.info("Starting sync raw data by UPC: {}", sourceData.getUpc());
    
    if (StringUtils.isBlank(sourceData.getUpc())) {
      log.warn("UPC is blank, skipping sync");
      return;
    }
    
    // Find all raw data with the same UPC
    List<MasterCatalogRawData> rawDataList = masterCatalogRawDataRepository.findAllByUpc(sourceData.getUpc());
    
    int syncedCount = 0;
    
    for (MasterCatalogRawData rawData : rawDataList) {
      // Skip the source data itself
      if (rawData.getId().equals(sourceData.getId())) {
        continue;
      }

      MasterCatalogRawData previousRawData = new MasterCatalogRawData();
      BeanUtils.copyProperties(rawData, previousRawData);
      previousRawData.setImages(masterCatalogImageRepository.findByMasterCatalogRawDataId(rawData.getId()));
      
      // Update the raw data properties
      rawData.setName(sourceData.getName());
      rawData.setDescription(sourceData.getDescription());
      rawData.setBrand(sourceData.getBrand());
      rawData.setPackageSize(sourceData.getPackageSize());
      rawData.setPackageType(sourceData.getPackageType());
      rawData.setDepartment(sourceData.getDepartment());
      rawData.setCategory(sourceData.getCategory());
      rawData.setSubCategory(sourceData.getSubCategory());
      rawData.setClazz(sourceData.getClazz());
      rawData.setPrimaryVendor(sourceData.getPrimaryVendor());
      rawData.setPrice(sourceData.getPrice());
      
      // Save the updated raw data
      MasterCatalogRawData currentRawData = masterCatalogRawDataRepository.save(rawData);
      
      // Delete existing images for this raw data
      masterCatalogImageRepository.deleteByMasterCatalogRawDataId(rawData.getId());
      
      // Sync images from source data if they exist
      if (sourceData.getImages() != null && !sourceData.getImages().isEmpty()) {
        List<MasterCatalogImage> newImages = sourceData.getImages().stream()
            .map(sourceImage -> MasterCatalogImage.builder()
                .imagePath(sourceImage.getImagePath())
                .masterCatalogRawDataId(rawData.getId())
                .primaryImage(sourceImage.getPrimaryImage())
                .build())
            .collect(Collectors.toList());
        
        List<MasterCatalogImage> masterCatalogImages = masterCatalogImageRepository.saveAll(newImages);
        currentRawData.setImages(masterCatalogImages);
      }

      applicationEventPublisherProvider.publishMasterCatalogRawDataSyncedEvent(
          currentRawData.getId(), currentRawData, previousRawData);
      
      syncedCount++;
    }
    
    log.info("Synced {} raw data records for UPC: {}", syncedCount, sourceData.getUpc());
  }
  
}
