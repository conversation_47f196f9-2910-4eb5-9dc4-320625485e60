package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyAssociateProduct;
import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyAssociateProductMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyAssociateProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyAssociateProductService;
import jakarta.persistence.criteria.Predicate;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import static com.mercaso.data.master_catalog.constants.CommonConstants.*;

@Slf4j
@Service
@Setter
@RequiredArgsConstructor
public class MasterCatalogPotentiallyAssociateProductServiceImpl implements
    MasterCatalogPotentiallyAssociateProductService {

  @Value("${master-catalog.task-records:20}")
  private int TASK_RECORD_COUNT;

  private final S3OperationAdapter s3OperationAdapter;
  private final MasterCatalogTaskService masterCatalogTaskService;
  private final MasterCatalogBatchJobRepository batchJobRepository;
  private final MasterCatalogTaskRepository masterCatalogTaskRepository;
  private final MasterCatalogImageRepository masterCatalogImageRepository;
  private final MasterCatalogProductRepository masterCatalogProductRepository;
  private final MasterCatalogProductAssociationRepository productAssociationRepository;
  private final MasterCatalogPotentiallyAssociateProductRepository potentiallyAssociateProductRepository;
  private final MasterCatalogPotentiallyAssociateProductMapper masterCatalogPotentiallyAssociateProductMapper;

  @Override
  public Page<MasterCatalogPotentiallyAssociateProductDto> search(UUID taskId, PotentiallyAssociateProductStatus status,
      PageRequest pageRequest) {

    Specification<MasterCatalogPotentiallyAssociateProduct> specification = ((root, query, builder) -> {
      Predicate taskIdPredicate = builder.equal(root.get("taskId"), taskId);
      if (status != null) {
        Predicate statusPredicate = builder.equal(root.get("status"), status);
        return builder.and(taskIdPredicate, statusPredicate);
      }
      return taskIdPredicate;
    });
    Page<MasterCatalogPotentiallyAssociateProduct> associateProductPage = potentiallyAssociateProductRepository.findAll(
        specification,
        pageRequest);

    return mapProductsWithImages(associateProductPage);
  }

  @Override
  @Transactional
  public MasterCatalogPotentiallyAssociateProductDto update(UUID id, boolean associated) {
    MasterCatalogPotentiallyAssociateProduct associateProduct = potentiallyAssociateProductRepository
        .findById(id).orElseThrow(
            () -> new MasterCatalogBusinessException(
                ErrorCodeEnums.MASTER_CATALOG_POTENTIALLY_ASSOCIATION_PRODUCT_NOT_FOUND,
                "potentially association product not found with id: " + id));

    MasterCatalogTask task = masterCatalogTaskService.validateUserPermission(associateProduct.getTaskId(), "review");

    // Validate data status
    validateDataStatus(associateProduct);
    // Validate job status
    validateJobStatus(associateProduct.getJobId());

    // Update data
    if (MasterCatalogTaskStatus.ASSIGNED == task.getStatus()) {
      task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);
      masterCatalogTaskRepository.save(task);
    }

    associateProduct.setAssociated(associated);
    associateProduct.setStatus(PotentiallyAssociateProductStatus.IN_STAGE);
    // Save and return result
    MasterCatalogPotentiallyAssociateProduct saved = potentiallyAssociateProductRepository.save(
        associateProduct);

    return masterCatalogPotentiallyAssociateProductMapper.toDto(saved);
  }

  private Page<MasterCatalogPotentiallyAssociateProductDto> mapProductsWithImages(
      Page<MasterCatalogPotentiallyAssociateProduct> products) {
    if (products.isEmpty()) {
      log.info("No products found");
      return Page.empty();
    }

    Map<UUID, UUID> rawDataIdToProductIdMap = getRawDataIdToProductIdMap(products);
    List<UUID> rawDataIds = new ArrayList<>(rawDataIdToProductIdMap.keySet());
    if (CollectionUtils.isEmpty(rawDataIds)) {
      log.info("No raw data IDs found in products");
      return Page.empty();
    }

    log.info("rawDataIds: {}", rawDataIds);

    List<MasterCatalogImage> allImages = masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(rawDataIds);
    Map<UUID, List<String>> imageMap = buildImageMap(allImages, rawDataIdToProductIdMap);

    return products.map(product -> {
      MasterCatalogPotentiallyAssociateProductDto dto = masterCatalogPotentiallyAssociateProductMapper.toDto(product);
      List<String> productImages = imageMap.getOrDefault(product.getProductId(), Collections.emptyList());
      List<String> potentiallyAssociateImages = imageMap.getOrDefault(product.getPotentiallyAssociateProductId(),
          Collections.emptyList());

      dto.setProductImages(productImages);
      dto.setPotentiallyAssociateProductImages(potentiallyAssociateImages);
      return dto;
    });
  }

  private Map<UUID, List<String>> buildImageMap(List<MasterCatalogImage> allImages,
      Map<UUID, UUID> rawDataIdToProductIdMap) {
    return allImages.stream()
        .peek(image -> image.setImagePath(s3OperationAdapter.getSignedUrl(image.getImagePath())))
        .filter(image -> rawDataIdToProductIdMap.containsKey(image.getMasterCatalogRawDataId()))
        .collect(Collectors.groupingBy(
            image -> rawDataIdToProductIdMap.get(image.getMasterCatalogRawDataId()),
            Collectors.mapping(MasterCatalogImage::getImagePath, Collectors.toList())));
  }

  private Map<UUID, UUID> getRawDataIdToProductIdMap(Page<MasterCatalogPotentiallyAssociateProduct> products) {
    List<UUID> productIds = products.stream().flatMap(product -> Stream.of(
        product.getPotentiallyAssociateProductId(),
        product.getProductId()
    )).toList();

    return masterCatalogProductRepository.findAllById(productIds).stream()
        .collect(Collectors.toMap(MasterCatalogProduct::getMasterCatalogRawDataId, MasterCatalogProduct::getId));
  }

  private void validateDataStatus(MasterCatalogPotentiallyAssociateProduct potentiallyAssociateProduct) {
    if (potentiallyAssociateProduct.getStatus() == PotentiallyAssociateProductStatus.COMPLETED) {
      throw new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_PRODUCT_ALREADY_REVIEWED,
          "The data is already reviewed, cannot update.");
    }
  }

  private void validateJobStatus(UUID jobId) {
    MasterCatalogBatchJob job = batchJobRepository.findById(jobId).orElseThrow(() ->
        new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_BATCH_JOB_NOT_FOUND,
            "job not found with id: " + jobId));

    if (!MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS.equals(job.getStatus())) {
      throw new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_BATCH_JOB_NOT_AVAILABLE,
          "The job is not in available submit job status: " + job.getStatus() + ", cannot update.");
    }
  }

  @Override
  public List<MasterCatalogPotentiallyAssociateProductDto> processPotentiallyAssociateProduct(UUID jobId,
      List<List<UUID>> productIds) {

    List<List<UUID>> associatedIds = productIds.stream()
        .filter(list -> list.size() == 2)
        .toList();

    int taskCount = (associatedIds.size() + TASK_RECORD_COUNT - 1) / TASK_RECORD_COUNT;
    List<MasterCatalogTaskDto> tasks = masterCatalogTaskService.createTasks(jobId,
        MasterCatalogTaskType.CREATE_ASSOCIATION, taskCount);
    List<UUID> taskIds = tasks.stream().map(MasterCatalogTaskDto::getId).toList();

    List<MasterCatalogPotentiallyAssociateProduct> associateProducts = new ArrayList<>();
    int index = 0;
    for (int i = 0; i < taskCount; i++) {
      UUID taskId = taskIds.get(i);
      int endIndex = Math.min(index + TASK_RECORD_COUNT, associatedIds.size());
      List<List<UUID>> subList = associatedIds.subList(index, endIndex);

      for (List<UUID> associatedIdList : subList) {
        MasterCatalogPotentiallyAssociateProduct entity =
            buildPotentiallyAssociateProduct(associatedIdList, jobId, taskId);
        if (entity != null) {
          associateProducts.add(entity);
        }
      }
      index = endIndex;

      if (index >= associatedIds.size()) {
        break;
      }
    }

    potentiallyAssociateProductRepository.saveAll(associateProducts);
    return associateProducts.stream()
        .map(masterCatalogPotentiallyAssociateProductMapper::toDto)
        .toList();
  }

  @Override
  @Transactional
  public void submit(List<UUID> potentiallyAssociateProductIds) {

    // Fetch data to be submitted
    List<MasterCatalogPotentiallyAssociateProduct> waitingSubmit = potentiallyAssociateProductRepository
        .findAllById(potentiallyAssociateProductIds);
    log.info("Submitting potentially associate, found {} potentially associate product", waitingSubmit.size());

    if (waitingSubmit.isEmpty()) {
      log.info("Submitting potentially associate, no data to submit");
      return;
    }

    Set<UUID> taskIdSet = waitingSubmit.stream()
        .map(MasterCatalogPotentiallyAssociateProduct::getTaskId)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());

    if (taskIdSet.size() != 1) {
      log.warn("no or multiple associate product not belong to the same task {}", taskIdSet);
      throw new MasterCatalogBusinessException(ErrorCodeEnums.FOUND_NON_OR_MULTIPLE_TASK,
          "no or multiple duplication product not belong to the same task");
    }
    //check if user has enough permission to access the data
    MasterCatalogTask task = masterCatalogTaskService.validateUserPermission(taskIdSet.iterator().next(),
        "submit");

    // Filter data with IN_STAGE status
    List<MasterCatalogPotentiallyAssociateProduct> validToSubmit = waitingSubmit.stream()
        .filter(data -> data.getStatus() == PotentiallyAssociateProductStatus.IN_STAGE)
        .toList();
    log.info("Submitting potentially associate, found {} valid data to submit", validToSubmit.size());

    if (validToSubmit.isEmpty()) {
      log.info("Submitting potentially associate, no valid data to submit because of the status is not in stage.");
      return;
    }

    processSubmit(task, validToSubmit);
  }

  private void processSubmit(MasterCatalogTask task, List<MasterCatalogPotentiallyAssociateProduct> validToSubmit) {

    MasterCatalogBatchJob masterCatalogBatchJob = validToSubmit.stream()
        .map(MasterCatalogPotentiallyAssociateProduct::getJobId).findFirst()
        .map(this::getAndValidateJob).get();

    validToSubmit.forEach(data -> data.setStatus(PotentiallyAssociateProductStatus.COMPLETED));

    potentiallyAssociateProductRepository.saveAll(validToSubmit);

    // updated task status
    checkIfUpdateTaskStatus(task);
    //create association
    createAssociation(validToSubmit);
    //mark product as associated
    markProductAsAssociated(validToSubmit);
    // updated job status
    checkIfUpdateJobStatus(masterCatalogBatchJob);
  }

  private void markProductAsAssociated(List<MasterCatalogPotentiallyAssociateProduct> validToSubmit) {
    Set<UUID> productIds = validToSubmit.stream()
        .filter(MasterCatalogPotentiallyAssociateProduct::getAssociated)
        .map(product ->
            List.of(product.getProductId(),
                product.getPotentiallyAssociateProductId()))
        .flatMap(List::stream).collect(Collectors.toSet());
    masterCatalogProductRepository.markAsAssociated(productIds);
  }

  private void checkIfUpdateJobStatus(MasterCatalogBatchJob masterCatalogBatchJob) {
    List<MasterCatalogTask> tasks = masterCatalogTaskRepository.findByJobId(masterCatalogBatchJob.getId());

    if (tasks.stream().allMatch(task -> task.getStatus() == MasterCatalogTaskStatus.COMPLETED)) {
      masterCatalogBatchJob.setStatus(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_COMPLETED);
      masterCatalogBatchJob.setCompletedAt(Instant.now());
      batchJobRepository.save(masterCatalogBatchJob);
      log.info("Job {} create association completed with status {}", masterCatalogBatchJob.getId(),
          masterCatalogBatchJob.getStatus());
    }
  }

  private void createAssociation(List<MasterCatalogPotentiallyAssociateProduct> potentiallyAssociateProducts) {
    List<MasterCatalogPotentiallyAssociateProduct> toBeAssociation = potentiallyAssociateProducts.stream()
        .filter(MasterCatalogPotentiallyAssociateProduct::getAssociated).toList();

    Set<String> productUpcs = toBeAssociation.stream()
        .flatMap(p -> Stream.of(p.getProductUpc(), p.getPotentiallyAssociateProductUpc()))
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());

    List<MasterCatalogProductAssociation> existingAssociations =
        productAssociationRepository.findAllByUpcIn(new ArrayList<>(productUpcs));

    Map<String, List<UUID>> upcToGroupMap = existingAssociations.stream()
        .collect(Collectors.groupingBy(MasterCatalogProductAssociation::getUpc,
            Collectors.mapping(MasterCatalogProductAssociation::getAssociationGroup, Collectors.toList())));

    List<MasterCatalogProductAssociation> associationList = new ArrayList<>();
    for (MasterCatalogPotentiallyAssociateProduct associateProduct : toBeAssociation) {
      String productUpc = associateProduct.getProductUpc();
      String potentialUpc = associateProduct.getPotentiallyAssociateProductUpc();
      List<String> upcs = List.of(productUpc, potentialUpc);

      List<UUID> productGroups = upcToGroupMap.get(productUpc);
      List<UUID> potentialGroups = upcToGroupMap.get(potentialUpc);

      if ((productGroups != null && (productGroups.size() > 1))
          || (potentialGroups != null && potentialGroups.size() > 1)) {
        log.warn("the upc {} or {} found in two different association groups, group: {} with group: {}",
            productUpc, potentialUpc, productGroups, potentialGroups);
        throw new MasterCatalogBusinessException(ErrorCodeEnums.UPC_FOUND_IN_TWO_DIFFERENT_ASSOCIATION_GROUPS,
            "the upc found in two different association groups");
      }

      //if both product and potential product are already in the different group, merge them
      if (productGroups != null && potentialGroups != null && !productGroups.getFirst()
          .equals(potentialGroups.getFirst())) {
        List<MasterCatalogProductAssociation> groups = productAssociationRepository.findByAssociationGroupIn(
            List.of(productGroups.getFirst(), potentialGroups.getFirst()));
        UUID associationGroup = groups.stream()
            .filter(g -> g.getAssociationGroup() != null)
            .min(Comparator.comparing(MasterCatalogProductAssociation::getCreatedAt))
            .map(MasterCatalogProductAssociation::getAssociationGroup).get();
        groups.forEach(group -> {
          group.setAssociationGroup(associationGroup);
        });
        productAssociationRepository.saveAll(groups);
        log.info("successful updated {} associations", groups.size());
      } else {
        UUID groupId = upcs.stream()
            .map(upcToGroupMap::get)
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .findFirst()
            .orElse(UUID.randomUUID());

        for (String upc : upcs) {
          if (upcToGroupMap.get(upc) == null || !upcToGroupMap.get(upc).contains(groupId)) {
            MasterCatalogProductAssociation association = new MasterCatalogProductAssociation();
            association.setId(UUID.randomUUID());
            association.setUpc(upc);
            association.setAssociationGroup(groupId);
            associationList.add(association);
          }
        }
      }
    }
    productAssociationRepository.saveAll(associationList);
    log.info("successful created {} associations", associationList.size());
  }

  private void checkIfUpdateTaskStatus(MasterCatalogTask task) {

    List<MasterCatalogPotentiallyAssociateProduct> potentiallyAssociateProducts = potentiallyAssociateProductRepository.findByTaskId(
        task.getId());
    Set<PotentiallyAssociateProductStatus> statusesSet = potentiallyAssociateProducts.stream()
        .map(MasterCatalogPotentiallyAssociateProduct::getStatus).collect(Collectors.toSet());
    if (statusesSet.stream().allMatch(
        PotentiallyAssociateProductStatus.COMPLETED::equals)) {
      task.setStatus(MasterCatalogTaskStatus.COMPLETED);
      masterCatalogTaskRepository.save(task);
    }
  }

  private MasterCatalogPotentiallyAssociateProduct buildPotentiallyAssociateProduct(
      List<UUID> associatedIds, UUID jobId, UUID taskId) {

    Optional<MasterCatalogProduct> productOptional = masterCatalogProductRepository.findById(
        associatedIds.getFirst());
    Optional<MasterCatalogProduct> potentialAssocateOptional = masterCatalogProductRepository.findById(
        associatedIds.getLast());

    //if cannot find by id
    if (productOptional.isEmpty()
        || potentialAssocateOptional.isEmpty()) {
      log.error("Create Potentially Associate Product Error, jobId: {}, for UUIDs: {}",
          jobId, associatedIds);
      return null;
    }

    MasterCatalogProduct product = productOptional.get();
    MasterCatalogProduct potentialAssociateProduct = potentialAssocateOptional.get();

    return masterCatalogPotentiallyAssociateProductMapper.mapProductToPotentialAssociateProduct(
        UUID.randomUUID(), jobId, taskId, product, potentialAssociateProduct,
        PotentiallyAssociateProductStatus.PENDING_REVIEW);
  }

  private MasterCatalogBatchJob getAndValidateJob(UUID jobId) {
    MasterCatalogBatchJob job = batchJobRepository.findById(jobId).orElseThrow(() ->
        new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_BATCH_JOB_NOT_FOUND,
            "job not found with id: " + jobId));

    if (!MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS.equals(job.getStatus())) {
      throw new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_BATCH_JOB_NOT_AVAILABLE,
          "The job is not in available submit job status: " + job.getStatus() + ", cannot update.");
    }
    return job;
  }
}
