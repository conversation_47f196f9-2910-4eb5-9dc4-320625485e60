package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.service.SuqareInventoryMetricsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("master-catalog/v1/square-inventory-metrics")
public class SuqareInventoryMetricsResource {

    private final SuqareInventoryMetricsService squareInventoryMetricsService;

    @PreAuthorize("hasAnyAuthority('master-catalog:write:square-inventory-metrics')")
    @PostMapping("/daily-metrics/init")
    public ResponseEntity<Void> initDailyMetrics(@RequestParam Integer daysBefore) {
        log.info("initDailyMetrics request, dayBefore: {}.", daysBefore);
        squareInventoryMetricsService.initDailyMetrics(daysBefore);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:write:square-inventory-metrics')")
    @PostMapping("/daily-metrics")
    public ResponseEntity<Void> calculateYesterdayMetrics() {
        squareInventoryMetricsService.calculateYesterdayMetrics();
        return ResponseEntity.ok().build();
    }
}
