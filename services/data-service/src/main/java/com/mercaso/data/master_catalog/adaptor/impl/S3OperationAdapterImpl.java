package com.mercaso.data.master_catalog.adaptor.impl;

import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.config.DocumentStorageMasterCatalogProperties;
import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import com.mercaso.data.master_catalog.dto.external.UploadDocumentRequestDto;
import com.mercaso.data.master_catalog.mapper.DocumentResponseDtoMapper;
import com.mercaso.data.master_catalog.mapper.UploadDocumentRequestDtoMapper;
import com.mercaso.document.operations.operations.DocumentOperations;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class S3OperationAdapterImpl implements S3OperationAdapter {

    private final DocumentOperations documentOperations;
    private final DocumentResponseDtoMapper documentResponseDtoMapper;
    private final UploadDocumentRequestDtoMapper uploadDocumentRequestDtoMapper;
    private final DocumentStorageMasterCatalogProperties documentStorageMasterCatalogProperties;

    @Override
    public DocumentResponseDto upload(UploadDocumentRequestDto uploadDocumentRequestDto) {

        uploadDocumentRequestDto.setDocumentName(
            documentStorageMasterCatalogProperties.getRootFolder() + uploadDocumentRequestDto.getDocumentName());

        return documentResponseDtoMapper.fromExternalDto(documentOperations.uploadDocument(uploadDocumentRequestDtoMapper.toExternalDto(
            uploadDocumentRequestDto)));
    }

    @Override
    public String getSignedUrl(String documentName) {
        return documentOperations.getSignedUrl(documentStorageMasterCatalogProperties.getRootFolder() + documentName);
    }
}
