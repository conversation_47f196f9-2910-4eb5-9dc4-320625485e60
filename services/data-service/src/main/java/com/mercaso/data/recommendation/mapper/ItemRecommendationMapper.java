package com.mercaso.data.recommendation.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.entity.ItemRecommendation;

@Mapper(componentModel = "spring")
public interface ItemRecommendationMapper {

  ItemRecommendationMapper INSTANCE = Mappers.getMapper(ItemRecommendationMapper.class);

  /**
   * Convert ItemRecommendation entity to ItemRecommendationDto.
   *
   * @param itemRecommendation the entity to convert
   * @return the converted DTO
   */
  @Mapping(source = "skuNumber", target = "sku")
  @Mapping(source = "reason", target = "reason.type")
  @Mapping(source = "reasonValue", target = "reason.value", qualifiedByName = "stringToInteger")
  @Mapping(source = "version", target = "version")
  @Mapping(source = "highPrice", target = "highPrice")
  @Mapping(source = "lowPrice", target = "lowPrice")
  @Mapping(source = "avgPrice", target = "avgPrice")
  @Mapping(source = "cost", target = "cost")
  ItemRecommendationDto toDto(ItemRecommendation itemRecommendation);

  /**
   * Converts a string reasonValue to Integer with rounding. Uses mathematical rounding to get the
   * nearest integer value.
   *
   * @param value the string value to convert
   * @return the converted Integer value rounded to the nearest integer, or null if conversion
   *         fails
   */
  @Named("stringToInteger")
  default Integer stringToInteger(String value) {
    if (value == null || value.isEmpty()) {
      return null;
    }
    try {
      double doubleValue = Double.parseDouble(value);
      return (int) Math.round(doubleValue);
    } catch (NumberFormatException e) {
      return null;
    }
  }
}
