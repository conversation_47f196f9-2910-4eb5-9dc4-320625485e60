package com.mercaso.data.image_management.service;

import com.mercaso.data.image_management.dto.ImageManagementItemImageDto;
import com.mercaso.data.image_management.dto.ImageSearchRequestDto;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

public interface ImageManagementImageService {

  String uploadAndSave(String shotAt,
      String fileName,
      ImageTypeEnum imageType,
      MultipartFile file);

  Page<ImageManagementItemImageDto> searchImages(
      ImageSearchRequestDto request);
}
