package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.service.SquareAuthorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/oauth/callback")
@RequiredArgsConstructor
@Slf4j
public class AuthorizationCallbackResource {

    private final SquareAuthorizationService squareAuthorizationService;

    @GetMapping("/square")
    public String executeSquareCallback(@RequestParam String code, @RequestParam String state) {
        log.info("OAuth callback from Square, code = {}, state = {}", code, state);
        squareAuthorizationService.obtainTokenByAuthorizationCode(code, state);
        return "Authorize Success!";
    }
}
