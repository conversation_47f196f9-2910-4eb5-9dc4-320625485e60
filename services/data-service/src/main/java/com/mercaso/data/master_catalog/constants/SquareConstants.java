package com.mercaso.data.master_catalog.constants;

import java.util.List;
import java.util.Map;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class SquareConstants {

    public static final long DAYS_IN_ONE_YEAR = 365;
    public static final int SQUARE_API_LIMIT = 1000;

    public static final String SQUARE_ROSIE_STORE_ID = "ecb65679-b851-498a-beee-e31c8926f123";
    public static final String SQUARE_KRYSTAL_KLEEN_STORE_ID = "be5437d9-4c6b-4518-a570-4d77df654488";
    public static final String NRS_HOUSE_BOOZE_STORE_ID = "86d0dd32-43c2-46cc-8f8e-e45f2f22d365";
    public static final String NRS_FICKETT_MARKET_STORE_ID = "cbf7e41b-4607-46d1-bb32-b4d723c07a84";
    public static final String NRS_LA_WEST_LIQUOR = "dd83ad51-d224-4d79-bd3c-99665214e519";


    // Key is the storeId, value is the customerId. The customerId is the id from Shopify product environment
    public static final Map<String, String> REPLENISHMENT_STORE_TO_CUSTOM = Map.of(
        SQUARE_ROSIE_STORE_ID, "5937488068859",
        SQUARE_KRYSTAL_KLEEN_STORE_ID, "5936623976699",
        NRS_HOUSE_BOOZE_STORE_ID, "7311232237819",
        NRS_FICKETT_MARKET_STORE_ID, "6944015745275",
        NRS_LA_WEST_LIQUOR, "6931466060027"
    );

    public static final List<String> REPLENISHMENT_STORE_IDS = List.of(
        SQUARE_ROSIE_STORE_ID,
        SQUARE_KRYSTAL_KLEEN_STORE_ID
    );
}
