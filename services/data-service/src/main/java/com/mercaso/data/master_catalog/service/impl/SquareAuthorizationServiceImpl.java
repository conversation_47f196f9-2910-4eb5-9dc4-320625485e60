package com.mercaso.data.master_catalog.service.impl;

import static com.mercaso.data.master_catalog.listener.SquareInventoryChangeListener.META_DATA_KEY_LOCATION_ID;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.mapper.MasterCatalogLocationMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import com.mercaso.data.master_catalog.service.SquareAuthorizationService;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class SquareAuthorizationServiceImpl implements SquareAuthorizationService {

    private final MasterCatalogSquareAuthorizationRepository authorizationRepository;
    private final SquareApiAdapter squareApiAdapter;
    private final CipherUtility cipherUtility;
    private final MasterCatalogLocationMapper masterCatalogLocationMapper;
    private final MasterCatalogLocationRepository masterCatalogLocationRepository;

    @Override
    public void obtainTokenByAuthorizationCode(String code, String state) {
        if (!StringUtils.hasText(code) || !StringUtils.hasText(state)) {
            log.error("Invalid parameters: code or state is empty");
            return;
        }

        log.info("Obtaining tokens from Square for state: {}", state);

        MasterCatalogSquareAuthorization authorization = authorizationRepository.findByState(UUID.fromString(state));
        if (authorization == null) {
            log.error("No authorization found for state: {}", state);
            return;
        }

        try {
            String decryptedAppId = cipherUtility.decrypt(authorization.getEncryptedApplicationId());
            String decryptedAppSecret = cipherUtility.decrypt(authorization.getEncryptedApplicationSecret());

            SquareObtainTokenResponse tokenResponse = squareApiAdapter.obtainTokenByAuthorizationCode(
                code, decryptedAppId, decryptedAppSecret);

            updateAuthorization(authorization, tokenResponse);
            MasterCatalogSquareAuthorization saved = authorizationRepository.save(authorization);

            log.info("Successfully obtained and saved Square tokens for state: {}", state);

            this.getAndSaveLocations(saved.getStoreId());
        } catch (Exception e) {
            log.error("Failed to obtain Square tokens for state: {}. Error: {}", state, e.getMessage(), e);
        }
    }

    private void getAndSaveLocations(UUID storeId) {
        List<MasterCatalogLocationDto> masterCatalogLocations = squareApiAdapter.listLocations(storeId);
        List<MasterCatalogLocation> locations = mapToEntities(masterCatalogLocations);

        Set<String> existingLocationIds = getExistingLocationIds(storeId);
        log.info("Found {} existing locations for storeId: {}", existingLocationIds, storeId);

        locations.removeIf(location -> isLocationExists(existingLocationIds, location));

        if (CollectionUtils.isNotEmpty(locations)) {
            updateStoreIdForLocations(locations, storeId);
            masterCatalogLocationRepository.saveAll(locations);
            log.info("Saved {} new locations.", locations.size());
        } else {
            log.info("No new locations to save.");
        }
    }

    private List<MasterCatalogLocation> mapToEntities(List<MasterCatalogLocationDto> masterCatalogLocations) {
        return masterCatalogLocations.stream()
            .map(masterCatalogLocationMapper::toEntity)
            .collect(Collectors.toList());
    }

    private Set<String> getExistingLocationIds(UUID storeId) {
        List<MasterCatalogLocation> existingLocations = masterCatalogLocationRepository.findAllByStoreId(storeId);
        return existingLocations.stream()
            .map(existing -> existing.getMetadata().get(META_DATA_KEY_LOCATION_ID).asText())
            .collect(Collectors.toSet());
    }

    private boolean isLocationExists(Set<String> existingLocationIds, MasterCatalogLocation location) {
        String locationId = location.getMetadata().get(META_DATA_KEY_LOCATION_ID).asText();
        boolean exists = existingLocationIds.contains(locationId);
        if (exists) {
            log.info("Location [{}] already exists and will be removed.", locationId);
        }
        return exists;
    }

    private void updateStoreIdForLocations(List<MasterCatalogLocation> locations, UUID storeId) {
        locations.forEach(location -> location.setStoreId(storeId));
    }

    private void updateAuthorization(MasterCatalogSquareAuthorization authorization,
        SquareObtainTokenResponse tokenResponse) {
        try {
            authorization.setEncryptedAccessToken(cipherUtility.encrypt(tokenResponse.accessToken()));
            authorization.setEncryptedRefreshToken(cipherUtility.encrypt(tokenResponse.refreshToken()));
            authorization.setAccessTokenExpiresAt(Instant.parse(tokenResponse.expiresAt()));
        } catch (Exception e) {
            log.error("Failed to update authorization tokens", e);
        }
    }
}
