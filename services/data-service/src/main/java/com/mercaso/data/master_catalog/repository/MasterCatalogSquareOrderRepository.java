package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface MasterCatalogSquareOrderRepository extends JpaRepository<MasterCatalogSquareOrder, UUID> {

    List<MasterCatalogSquareOrder> findByIdInAndCreatedAtBetweenOrderByCreatedAtDesc(
        List<UUID> ids, Instant startDate, Instant endDate);

    Optional<MasterCatalogSquareOrder> findTopByStoreIdOrderByOrderCreatedAtDesc(UUID storeId);

    Optional<List<MasterCatalogSquareOrder>> findByStoreIdAndOrderCreatedAtAfter(UUID storeId, Instant createdAt);
}
