package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.constants.CommonConstants;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogPotentiallyDuplicateRawDataMapper extends
    BaseDoMapper<MasterCatalogPotentiallyDuplicateRawDataDto, MasterCatalogPotentiallyDuplicateRawData> {

    @Mapping(target = "potentiallyDuplicateRawDataId", source = "rawData.id")
    @Mapping(target = "potentiallyDuplicateName", source = "rawData.name")
    @Mapping(target = "potentiallyDuplicateUpc", source = "rawData.upc")
    @Mapping(target = "potentiallyDuplicateVendor", source = "rawData.primaryVendor")
    @Mapping(target = "name", ignore = true)
    @Mapping(target = "upc", ignore = true)
    @Mapping(target = "primaryVendor", ignore = true)
    @Mapping(target = "rawDataId", ignore = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "duplicated", expression = "java(Boolean.FALSE)")
    @Mapping(target = "createdBy", constant = CommonConstants.SYSTEM_USER_ID)
    @Mapping(target = "updatedBy", constant = CommonConstants.SYSTEM_USER_ID)
    @Mapping(target = "status", source = "status")
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    MasterCatalogPotentiallyDuplicateRawData mapRawDataToPotentiallyUnDuplicateRawData(
        UUID id,
        MasterCatalogRawData rawData,
        UUID jobId,
        PotentiallyDuplicateRawDataStatus status);

    @Mapping(source = "jobId", target = "jobId")
    @Mapping(source = "firstRawData.id", target = "rawDataId")
    @Mapping(source = "firstRawData.name", target = "name")
    @Mapping(source = "firstRawData.upc", target = "upc")
    @Mapping(source = "firstRawData.primaryVendor", target = "primaryVendor")
    @Mapping(source = "secondRawData.id", target = "potentiallyDuplicateRawDataId")
    @Mapping(source = "secondRawData.name", target = "potentiallyDuplicateName")
    @Mapping(source = "secondRawData.upc", target = "potentiallyDuplicateUpc")
    @Mapping(source = "secondRawData.primaryVendor", target = "potentiallyDuplicateVendor")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "createdBy", constant = CommonConstants.SYSTEM_USER_ID)
    @Mapping(target = "updatedBy", constant = CommonConstants.SYSTEM_USER_ID)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    MasterCatalogPotentiallyDuplicateRawData mapRawDataToPotentialDuplicateRawData(
        UUID id,
        UUID jobId,
        MasterCatalogRawData firstRawData,
        MasterCatalogRawData secondRawData,
        PotentiallyDuplicateRawDataStatus status);
}