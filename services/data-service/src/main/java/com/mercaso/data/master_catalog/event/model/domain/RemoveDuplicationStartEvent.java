package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationStartPayload;

@EventPublishConfig(
    publishLocal = true
)
public class RemoveDuplicationStartEvent extends BaseApplicationEvent<RemoveDuplicationStartPayload> {

    public RemoveDuplicationStartEvent(Object source, RemoveDuplicationStartPayload payload) {
        super(source, payload);
    }
} 