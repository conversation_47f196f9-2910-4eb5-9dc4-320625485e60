package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationRequestDto;
import java.util.UUID;

public interface MasterCatalogSquareAuthorizationService {

    MasterCatalogSquareAuthorizationDto createSquareAuthorization(
      MasterCatalogSquareAuthorizationRequestDto masterCatalogSquareAuthorizationRequestDto);

    MasterCatalogSquareAuthorizationDto getSquareAuthorization(UUID storeId);

    void refreshSquareAuthorization();
}
