package com.mercaso.data.recommendation.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mercaso.data.recommendation.ItemRecommendationService;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.RecommendationReasonDto;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/recommendation/v1/item-recommendations")
@RequiredArgsConstructor
public class ItemRecommendationResource {

  private static final String DEFAULT_STORE_ID = "b350491e-e238-4caf-b328-cf2438e057d8";

  private final ItemRecommendationService itemRecommendationService;
  private final Environment environment;

  private final List<ItemRecommendationDto> csvRecommendations = new ArrayList<>();

  @PostConstruct
  public void init() {
    loadCsvData();
  }

  @GetMapping("/search")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public PageableResponse<ItemRecommendationDto> search(
      @RequestParam(defaultValue = "0") Integer pageNumber,
      @RequestParam(defaultValue = "20") Integer pageSize,
      @RequestParam(required = false) String storeId,
      @RequestParam(required = false, defaultValue = "V1") String version,
      @RequestParam(required = false) String departmentId,
      @RequestParam(required = false) List<String> reasons) {

    String effectiveStoreId = StringUtils.hasText(storeId) ? storeId : DEFAULT_STORE_ID;

    log.info(
        "Search item recommendations for store: {}, version: {}, departmentId: {}, reasons: {}, page: {}, size: {}",
        effectiveStoreId, version, departmentId, reasons, pageNumber, pageSize);

    if (shouldUseCsvData()) {
      log.info("Using CSV data for search (dev profile + test store ID)");
      return searchCsvData(effectiveStoreId, version, departmentId, reasons, pageNumber,
          pageSize);
    }

    return itemRecommendationService.search(effectiveStoreId, version, departmentId, reasons,
        pageNumber, pageSize);
  }

  @GetMapping("/departments")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public List<DepartmentDto> getDepartments(
      @RequestParam(required = false) String storeId,
      @RequestParam(required = false, defaultValue = "V1") String version) {

    String effectiveStoreId = StringUtils.hasText(storeId) ? storeId : DEFAULT_STORE_ID;

    log.info("Get distinct departments for store: {}, version: {}", effectiveStoreId, version);

    if (shouldUseCsvData()) {
      log.info("Using CSV data for departments (dev profile + test store ID)");
      return csvRecommendations.stream()
          .filter(dto -> effectiveStoreId.equals(dto.getStoreId()))
          .filter(dto -> version.equals(dto.getVersion()))
          .map(dto -> new DepartmentDto(dto.getDepartmentId(), dto.getDepartment()))
          .distinct()
          .collect(Collectors.toList());
    }

    return itemRecommendationService.getDepartments(effectiveStoreId, version);
  }

  private PageableResponse<ItemRecommendationDto> searchCsvData(
      String storeId, String version, String departmentId, List<String> reasons, Integer pageNumber,
      Integer pageSize) {

    List<ItemRecommendationDto> filteredData = csvRecommendations.stream()
        .filter(dto -> storeId.equals(dto.getStoreId()))
        .filter(dto -> version.equals(dto.getVersion()))
        .filter(dto -> {
          // For legacy versions (V1, V2), ignore departmentId filter
          if (isLegacyVersion(version)) {
            return true;
          }
          if (departmentId != null && !departmentId.isBlank()) {
            return departmentId.equals(dto.getDepartmentId());
          }
          return true;
        })
        .filter(dto -> {
          if (reasons != null && !reasons.isEmpty()) {
            return reasons.contains(dto.getReason().getType());
          }
          return true;
        })
        .sorted((a, b) -> Integer.compare(b.getReason().getValue(), a.getReason().getValue())) // Sort by reasonValue DESC
        .collect(Collectors.toList());

    int start = pageNumber * pageSize;
    int end = Math.min(start + pageSize, filteredData.size());

    List<ItemRecommendationDto> pagedData = filteredData.subList(
        Math.min(start, filteredData.size()),
        Math.min(end, filteredData.size())
    );

    return PageableResponse.<ItemRecommendationDto>builder()
        .data(pagedData)
        .pageNumber(pageNumber)
        .pageSize(pageSize)
        .totalPages((int) Math.ceil((double) filteredData.size() / pageSize))
        .totalElements(filteredData.size())
        .build();
  }

  private boolean isLegacyVersion(String version) {
    return "V1".equals(version) || "V2".equals(version);
  }

  private void loadCsvData() {
    try {
      BufferedReader reader = new BufferedReader(
          new InputStreamReader(
              new ClassPathResource("mock_item_recommendation.csv").getInputStream()));

      // Skip header
      String line = reader.readLine();

      while ((line = reader.readLine()) != null) {
        String[] data = line.split(",(?=([^\"]*\"[^\"]*\")*[^\"]*$)");
        if (data.length < 8) {
          log.warn("Invalid CSV line: {}", line);
          continue;
        }

        try {
          ItemRecommendationDto dto = ItemRecommendationDto.builder()
              // Add store_id as a field to track which store this record belongs to
              .storeId(cleanField(data[0]))
              .productId(cleanField(data[1]))
              .sku(cleanField(data[2]))
              .version(cleanField(data[3]))
              .departmentId(cleanField(data[4]))
              .department(cleanField(data[5]))
              .reason(RecommendationReasonDto.builder()
                  .type(cleanField(data[6]))
                  .value(Integer.parseInt(cleanField(data[7])))
                  .build())
              .highPrice(data.length > 8 ? cleanField(data[8]) : null)
              .lowPrice(data.length > 9 ? cleanField(data[9]) : null)
              .avgPrice(data.length > 10 ? cleanField(data[10]) : null)
              .cost(data.length > 11 && cleanField(data[11]) != null
                  ? new BigDecimal(cleanField(data[11]))
                  : null)
              .build();
          csvRecommendations.add(dto);
        } catch (Exception e) {
          log.error("Error parsing line: {}", line, e);
        }
      }

      log.info("Loaded {} item recommendations from CSV", csvRecommendations.size());
    } catch (IOException e) {
      log.error("Failed to load CSV data", e);
    }
  }

  private String cleanField(String field) {
    if (field == null || field.trim().isEmpty()) {
      return null;
    }
    return field.trim().replaceAll("^\"|\"$", "");
  }

  private boolean isDevProfile() {
    return Arrays.asList(environment.getActiveProfiles()).contains("dev");
  }

  private boolean shouldUseCsvData() {
    return isDevProfile();
  }
}
