package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationRequestDto;
import com.mercaso.data.master_catalog.service.MasterCatalogSquareAuthorizationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/square-authorization")
@RequiredArgsConstructor
@Slf4j
@Validated
public class MasterCatalogSquareAuthorizationResource {

    private final MasterCatalogSquareAuthorizationService squareAuthorizationService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('master-catalog:write:square-authorization')")
    public ResponseEntity<Void> createSquareAuthorization(@Valid
      @RequestBody MasterCatalogSquareAuthorizationRequestDto masterCatalogSquareAuthorizationRequestDto) {
        squareAuthorizationService.createSquareAuthorization(masterCatalogSquareAuthorizationRequestDto);

        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PostMapping("/refresh")
    @PreAuthorize("hasAnyAuthority('master-catalog:write:square-authorization')")
    public ResponseEntity<Void> refreshSquareAuthorization() {
        squareAuthorizationService.refreshSquareAuthorization();

        return new ResponseEntity<>(HttpStatus.OK);
    }
}
