package com.mercaso.data.master_catalog.dto;

import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogSquareAuthorizationDto extends BaseDto {

    private UUID id;

    private UUID state;

    private UUID storeId;

    private String encryptedAccessToken;

    private String encryptedRefreshToken;

    private Instant accessTokenExpiresAt;

    private String encryptedApplicationId;

    private String encryptedApplicationSecret;

    private String permissions;

    private Instant deletedAt;

    private Instant createdAt;

    private Instant updatedAt;
}
