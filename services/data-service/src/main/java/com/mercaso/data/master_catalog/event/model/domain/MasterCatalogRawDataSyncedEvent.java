package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataSyncedPayload;

@EventPublishConfig
public class MasterCatalogRawDataSyncedEvent extends BaseApplicationEvent<MasterCatalogRawDataSyncedPayload> {

    public MasterCatalogRawDataSyncedEvent(Object source, MasterCatalogRawDataSyncedPayload payload) {
        super(source, payload);
    }
}
