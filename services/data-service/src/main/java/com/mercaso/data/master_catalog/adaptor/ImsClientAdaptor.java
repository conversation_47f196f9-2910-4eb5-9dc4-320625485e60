package com.mercaso.data.master_catalog.adaptor;

import com.mercaso.ims.client.dto.ItemDto;
import java.util.List;
import java.util.Optional;

public interface ImsClientAdaptor {


  /**
   * Searches for item details by SKU from IMS
   * @param sku the SKU to search for
   * @return an Optional containing the ItemDto if found, or an empty Optional if not found
   */
  Optional<ItemDto> searchItemDetailBySku(String sku);

  /**
   * Searches for item details by UPC from IMS
   * @param upc the UPC to search for
   * @return an Item List
   */
  List<ItemDto> searchItemDetailByUpc(String upc);
}
