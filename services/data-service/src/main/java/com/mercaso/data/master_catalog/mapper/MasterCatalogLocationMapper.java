package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogLocationMapper extends
    BaseDoMapper<MasterCatalogLocationDto, MasterCatalogLocation> {

}