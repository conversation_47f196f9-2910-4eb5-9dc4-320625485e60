package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateRawDataRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateSubmitRequest;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyDuplicateRawDataService;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/potentially-duplicate-raw-data")
@RequiredArgsConstructor
@Validated
public class PotentiallyDuplicateRawDataController {

    private final MasterCatalogPotentiallyDuplicateRawDataService masterCatalogPotentiallyDuplicateRawDataService;

    @PreAuthorize("hasAnyAuthority('master-catalog:read:potentially-duplicate-raw-data')")
    @GetMapping("/list")
    public ResponseEntity<CustomPage<MasterCatalogPotentiallyDuplicateRawDataDto>> list(
        @Valid @ModelAttribute PotentiallyDuplicateRawDataRequest request) {

        return ResponseEntity.ok(new CustomPage<MasterCatalogPotentiallyDuplicateRawDataDto>().build(
            masterCatalogPotentiallyDuplicateRawDataService.list(request)));
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:write:potentially-duplicate-raw-data')")
    @PostMapping("/submit")
    public ResponseEntity<Void> submit(@Valid @RequestBody PotentiallyDuplicateSubmitRequest potentiallyDuplicateSubmitRequest) {
        masterCatalogPotentiallyDuplicateRawDataService.submit(potentiallyDuplicateSubmitRequest);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:write:potentially-duplicate-raw-data')")
    @PutMapping("/{id}")
    public ResponseEntity<MasterCatalogPotentiallyDuplicateRawDataDto> update(
        @PathVariable(value = "id") UUID id, @RequestBody MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request) {

        return ResponseEntity.ok(masterCatalogPotentiallyDuplicateRawDataService.update(id, request));
    }
}
