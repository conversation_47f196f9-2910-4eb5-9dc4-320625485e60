package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogRawDataMapper extends BaseDoMapper<MasterCatalogRawDataDto, MasterCatalogRawData> {

  @Mapping(target = "images", ignore = true)
  MasterCatalogRawData toEntity(MasterCatalogRawDataDto masterCatalogRawDataDto);

  @Mapping(target = "images", ignore = true)
  MasterCatalogRawDataDto toDto(MasterCatalogRawData masterCatalogRawData);
}