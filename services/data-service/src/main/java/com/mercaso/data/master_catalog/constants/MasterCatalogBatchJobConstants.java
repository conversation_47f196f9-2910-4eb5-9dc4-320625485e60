package com.mercaso.data.master_catalog.constants;

import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import java.util.List;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MasterCatalogBatchJobConstants {

    public static final List<MasterCatalogBatchJobStatus> IN_PROGRESS_STATUSES = List.of(
        MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS,
        MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_COMPLETED,
        MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_IN_PROGRESS
    );

    public static final List<PotentiallyDuplicateRawDataStatus> PENDING_STATUSES = List.of(
        PotentiallyDuplicateRawDataStatus.PENDING_REVIEW,
        PotentiallyDuplicateRawDataStatus.IN_STAGE
    );

    public static final List<MasterCatalogBatchJobStatus> AVAILABLE_SUBMIT_JOB_STATUSES = List.of(
        MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS,
        MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_IN_PROGRESS
    );

    public static final List<PotentiallyDuplicateRawDataStatus> AVAILABLE_GENERATE_PRODUCTS_STATUSES = List.of(
        PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED,
        PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED
    );

    public static final String DUPLICATE_FIELD_NAME_UPC = "upc";
    public static final String DUPLICATE_FIELD_NAME_DESCRIPTION = "description";
    public static final String DUPLICATE_FIELD_NAME_SKU = "sku";

    public static final String DEFAULT_BATCH_SIZE = "1000";
}
