package com.mercaso.data.master_catalog.event.core;

import com.mercaso.businessevents.client.BusinessEventClient;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventPublisher;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventType;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventTypeProvider;
import com.mercaso.data.master_catalog.event.factory.BusinessEventDtoFactory;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Spring event publisher implementation
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultApplicationEventPublisher implements ApplicationEventPublisher {

    private final org.springframework.context.ApplicationEventPublisher applicationEventPublisher;
    private final EventTypeRegistry eventTypeRegistry;
    private final BusinessEventClient businessEventClient;
    private final BusinessEventDtoFactory businessEventDtoFactory;

    @Override
    public <P extends BusinessEventPayload<?>, E extends Enum<?> & ApplicationEventTypeProvider> void publish(
        E eventType,
        P payload
    ) {
        try {
            String eventTypeName = eventType.getEventTypeName();
            Class<? extends BaseApplicationEvent<P>> eventClass = eventType.getApplicationEventClass();

            ApplicationEventType<?, ?> registeredApplicationEventType = eventTypeRegistry.findByEventClass(eventClass)
                .orElseThrow(() -> new IllegalArgumentException("Unknown event type: " + eventTypeName));

            if (registeredApplicationEventType.shouldPublishLocal()) {
                try {
                    Object event = registeredApplicationEventType.createEvent(this, payload);
                    log.info("Publishing event of type: {}", event.getClass().getName());
                    applicationEventPublisher.publishEvent(event);
                    log.debug("Published local event: {}", eventTypeName);
                } catch (Exception e) {
                    log.error("Failed to publish local event", e);
                    throw new RuntimeException("Failed to publish local event", e);
                }
            }

            if (registeredApplicationEventType.shouldPersist()) {
                BusinessEventDto dto = businessEventDtoFactory.createBusinessEventDto(
                    eventTypeName,
                    payload
                );
                businessEventClient.dispatch(dto);
                log.debug("Stored event: {}", eventTypeName);
            }

            log.info("Published event using enum type: {}", eventTypeName);
        } catch (Exception e) {
            log.error("Failed to publish event using enum", e);
            throw new RuntimeException("Event publish using enum failed", e);
        }
    }
}
