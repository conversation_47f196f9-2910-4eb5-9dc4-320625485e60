package com.mercaso.data.master_catalog.event.payload.domain;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.event.annotation.BusinessEntityIdentifier;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GenerateProductsSubmitPayload extends BusinessEventPayload<MasterCatalogBatchJobDto> {

    @BusinessEntityIdentifier(value = "Product generation job")
    private UUID batchJobId;

    private List<UUID> potentiallyDuplicateIds;

    @Builder
    public GenerateProductsSubmitPayload(@NotNull UUID batchJobId,
        MasterCatalogBatchJobDto payload, List<UUID> potentiallyDuplicateIds) {
        super(payload);
        this.batchJobId = batchJobId;
        this.potentiallyDuplicateIds = potentiallyDuplicateIds;
    }
}
