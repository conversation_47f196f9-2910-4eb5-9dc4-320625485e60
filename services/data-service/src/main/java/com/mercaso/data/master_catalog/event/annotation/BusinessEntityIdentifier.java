package com.mercaso.data.master_catalog.event.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface BusinessEntityIdentifier {

    /**
     * Entity type identifier value
     */
    String value();

    /**
     * Whether it must exist (default true)
     */
    boolean required() default true;
} 