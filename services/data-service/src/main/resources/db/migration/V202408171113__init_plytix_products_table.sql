-- Create shopify schema
CREATE SCHEMA plytix;

create table plytix.products
(
    id                      varchar(100) not null primary key,
    sku                     text         not null,
    attributes              jsonb,
    status                  text,
    num_variations          integer,
    product_family_id       uuid,
    product_family_model_id uuid,
    created                 timestamp with time zone,
    modified                timestamp with time zone,
    modified_user_audit     jsonb,
    created_user_audit      jsonb,
    overwritten_attributes  jsonb,
    categories              jsonb,
    assets                  jsonb,
    label                   text,
    _parent_id              varchar(50),
    thumbnail               jsonb
);

-- create sku index on products table
create index products_sku_index
    on plytix.products (sku);
