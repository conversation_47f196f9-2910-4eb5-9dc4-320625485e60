CREATE
EXTENSION IF NOT EXISTS "uuid-ossp";

create table master_catalog_nrs_raw_data (
    id uuid primary key default uuid_generate_v4(),
    barcode varchar(255) not null,
    raw_data jsonb not null,
    source_from varchar(255) not null,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone
);

create index idx_master_catalog_nrs_raw_data_barcode on master_catalog_nrs_raw_data (barcode);

comment on table master_catalog_nrs_raw_data is 'Source raw data for NRS Catalog';
comment on column master_catalog_nrs_raw_data.raw_data is 'Json data for NRS Catalog';
comment on column master_catalog_nrs_raw_data.source_from is 'Source from which the data is fetched, e.g. Barcode, Go UPC';

create table master_catalog_nrs_transaction (
    id uuid primary key default uuid_generate_v4(),
    store_id uuid not null,
    master_catalog_raw_data_id uuid not null,
    transaction_time timestamp with time zone not null,
    transaction_qty numeric not null default 0,
    transaction_amount numeric not null default 0,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone
);

create index idx_master_catalog_nrs_transaction_store_id on master_catalog_nrs_transaction (store_id);
create index idx_master_catalog_nrs_transaction_master_catalog_raw_data_id on master_catalog_nrs_transaction (master_catalog_raw_data_id);

comment on table master_catalog_nrs_transaction is 'Transaction data for NRS Catalog';
comment on column master_catalog_nrs_transaction.store_id is 'Store ID';
comment on column master_catalog_nrs_transaction.master_catalog_raw_data_id is 'Master Catalog Raw Data ID';
comment on column master_catalog_nrs_transaction.transaction_time is 'Transaction Time';
comment on column master_catalog_nrs_transaction.transaction_qty is 'Transaction Quantity';
comment on column master_catalog_nrs_transaction.transaction_amount is 'Transaction Amount';