create table if not exists store
(
    id         UUID primary key,
    name       varchar(255) not null,
    created_at timestamp    not null default now(),
    updated_at timestamp    not null default now()
);

insert into store (id, name) values ('b350491e-e238-4caf-b328-cf2438e057d8', 'Mercaso');

create table if not exists master_catalog_raw_data
(
    id             UUID primary key,
    store_id       UUID         not null,
    upc            varchar(64),
    name           varchar(255) not null,
    description    TEXT         not null,
    brand          varchar(64),
    sku_number     VARCHAR(255) NOT NULL,
    department     varchar(64),
    category       varchar(64),
    sub_category   varchar(64),
    clazz          varchar(64),
    primary_vendor varchar(64),
    created_at     timestamp    not null default now(),
    updated_at     timestamp    not null default now()
);

create index if not exists master_catalog_raw_data_store_id_idx on master_catalog_raw_data (store_id);
create index if not exists master_catalog_raw_data_upc_idx on master_catalog_raw_data (upc);

create table if not exists master_catalog_product
(
    id                         UUID primary key,
    upc                        varchar(64),
    name                       varchar(255) not null,
    description                TEXT         not null,
    brand                      varchar(64),
    sku_number                 VARCHAR(255) NOT NULL,
    department                 varchar(64),
    category                   varchar(64),
    sub_category               varchar(64),
    clazz                      varchar(64),
    primary_vendor             varchar(64),
    master_catalog_raw_data_id UUID         not null,
    created_at                 timestamp    not null default now(),
    updated_at                 timestamp    not null default now()
);

create index if not exists product_upc_idx on master_catalog_product (upc);

create table if not exists master_catalog_product_association
(
    id              UUID primary key,
    upc             varchar(64) not null,
    associated_upcs text        not null,
    created_at      timestamp   not null default now(),
    updated_at      timestamp   not null default now()
);

create index if not exists product_association_upc_idx on master_catalog_product_association (upc);

create table if not exists master_catalog_image
(
    id                         UUID primary key,
    image_path                 varchar   not null,
    master_catalog_raw_data_id UUID      not null,
    primary_image              boolean   not null default false,
    created_at                 timestamp not null default now(),
    updated_at                 timestamp not null default now()
);

create index if not exists master_catalog_image_master_catalog_raw_data_id_idx on master_catalog_image (master_catalog_raw_data_id);

