alter table master_catalog_raw_data
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_raw_data
    alter column updated_at type timestamptz using updated_at::timestamptz;

alter table master_catalog_image
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_image
    alter column updated_at type timestamptz using updated_at::timestamptz;

alter table master_catalog_inventory
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_inventory
    alter column updated_at type timestamptz using updated_at::timestamptz;

alter table master_catalog_product
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_product
    alter column updated_at type timestamptz using updated_at::timestamptz;

alter table master_catalog_product_association
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_product_association
    alter column updated_at type timestamptz using updated_at::timestamptz;

alter table master_catalog_raw_data_duplication
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_raw_data_duplication
    alter column updated_at type timestamptz using updated_at::timestamptz;

alter table master_catalog_square_variation_mapping
    alter column created_at type timestamptz using created_at::timestamptz;
alter table master_catalog_square_variation_mapping
    alter column updated_at type timestamptz using updated_at::timestamptz;
