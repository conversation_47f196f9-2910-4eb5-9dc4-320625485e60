CREATE TABLE IF NOT EXISTS store_profiling_store_continuous_metrics (
    id              BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    store_id        VARCHAR(50) NOT NULL,
    metric_category VARCHAR(255) NOT NULL,
    metric_name     VARCHAR(255) NOT NULL,
    metric_desc     text NOT NULL,
    metric_value    DECIMAL(10, 2) NOT NULL,
    metric_date     TIMESTAMP NOT NULL,
    metric_date_type VARCHAR(255) NOT NULL  
);

CREATE INDEX IF NOT EXISTS idx_spscm_metric_name_value ON store_profiling_store_continuous_metrics (metric_name, metric_value);
CREATE INDEX IF NOT EXISTS idx_spscm_store_id_metric_name ON store_profiling_store_continuous_metrics (store_id, metric_name);
CREATE INDEX IF NOT EXISTS idx_spscm_store_id ON store_profiling_store_continuous_metrics (store_id);
