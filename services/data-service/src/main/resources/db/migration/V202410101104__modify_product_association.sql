drop table if exists master_catalog_product_association;

create table if not exists master_catalog_product_association
(
    id                uuid primary key,
    upc               varchar(64) not null,
    association_group uuid        not null,
    created_at        timestamp   not null default now(),
    updated_at        timestamp   not null default now()
);

create index if not exists product_association_upc_idx on master_catalog_product_association (upc);
create index if not exists product_association_group_idx on master_catalog_product_association (association_group);