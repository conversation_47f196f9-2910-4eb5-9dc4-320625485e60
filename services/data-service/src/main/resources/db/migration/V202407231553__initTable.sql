-- Create shopify schema
CREATE SCHEMA shopify;

--- <PERSON>reate orders table
CREATE TABLE shopify.orders
(
    id                                 BIGINT PRIMARY KEY,
    admin_graphql_api_id               VARCHAR(255),
    app_id                             VARCHAR(50),
    browser_ip                         VARCHAR(45),
    buyer_accepts_marketing            BOOLEAN,
    cancel_reason                      VARCHAR(255),
    cancelled_at                       TIMESTAMPTZ,
    checkout_id                        BIGINT,
    client_details                     jsonb,
    closed_at                          TIMESTAMPTZ,
    confirmed                          BO<PERSON>EAN,
    contact_email                      VARCHAR(255),
    created_at                         TIMESTAMPTZ,
    currency                           CHAR(3),
    current_subtotal_price             DECIMAL(10, 2),
    current_subtotal_price_set         jsonb,
    current_total_additional_fees_set  jsonb,
    current_total_discounts            DECIMAL(10, 2),
    current_total_discounts_set        jsonb,
    current_total_duties_set           jsonb,
    current_total_price                DECIMAL(10, 2),
    current_total_price_set            jsonb,
    current_total_tax                  DECIMAL(10, 2),
    current_total_tax_set              jsonb,
    device_id                          BIGINT,
    discount_codes                     jsonb,
    duties_included                    BOOLEAN,
    email                              VARCHAR(255),
    estimated_taxes                    BOOLEAN,
    financial_status                   VARCHAR(100),
    fulfillment_status                 VARCHAR(100),
    location_id                        BIGINT,
    merchant_of_record_app_id          BIGINT,
    name                               VARCHAR(255),
    note                               TEXT,
    note_attributes                    jsonb,
    number                             INT,
    order_number                       INT,
    original_total_additional_fees_set jsonb,
    original_total_duties_set          jsonb,
    payment_gateway_names              jsonb,
    phone                              VARCHAR(255),
    po_number                          VARCHAR(255),
    presentment_currency               CHAR(3),
    processed_at                       TIMESTAMPTZ,
    subtotal_price                     DECIMAL(10, 2),
    subtotal_price_set                 jsonb,
    tags                               TEXT,
    tax_exempt                         BOOLEAN,
    tax_lines                          jsonb,
    taxes_included                     BOOLEAN,
    test                               BOOLEAN,
    total_discounts                    DECIMAL(10, 2),
    total_discounts_set                jsonb,
    total_line_items_price             DECIMAL(10, 2),
    total_line_items_price_set         jsonb,
    total_outstanding                  DECIMAL(10, 2),
    total_price                        DECIMAL(10, 2),
    total_price_set                    jsonb,
    total_shipping_price_set           jsonb,
    total_tax                          DECIMAL(10, 2),
    total_tax_set                      jsonb,
    total_tip_received                 DECIMAL(10, 2),
    total_weight                       INT,
    updated_at                         TIMESTAMPTZ,
    user_id                            BIGINT,
    billing_address                    jsonb,
    customer                           jsonb,
    discount_applications              jsonb,
    fulfillments                       jsonb,
    refunds                            jsonb,
    shipping_address                   jsonb,
    shipping_lines                     jsonb
);

--- Create orders_line_items table
CREATE TABLE shopify.orders_line_items
(
    id                           BIGINT PRIMARY KEY,
    order_id                     BIGINT,
    admin_graphql_api_id         VARCHAR(255),
    attributed_staffs            jsonb,
    current_quantity             INT,
    fulfillable_quantity         INT,
    fulfillment_service          VARCHAR(255),
    fulfillment_status           VARCHAR(255),
    gift_card                    BOOLEAN,
    grams                        INT,
    name                         VARCHAR(255),
    price                        DECIMAL(10, 2),
    price_set                    jsonb,
    product_exists               BOOLEAN,
    product_id                   BIGINT,
    properties                   jsonb,
    quantity                     INT,
    requires_shipping            BOOLEAN,
    sku                          VARCHAR(255),
    taxable                      BOOLEAN,
    title                        VARCHAR(255),
    total_discount               DECIMAL(10, 2),
    total_discount_set           jsonb,
    variant_id                   BIGINT,
    variant_inventory_management VARCHAR(255),
    variant_title                VARCHAR(255),
    vendor                       VARCHAR(255),
    tax_lines                    jsonb,
    duties                       jsonb,
    discount_allocations         jsonb
);

--- Create customers table
CREATE TABLE shopify.customers
(
    id                      BIGINT PRIMARY KEY,
    email                   VARCHAR(255),
    created_at              TIMESTAMPTZ,
    updated_at              TIMESTAMPTZ,
    first_name              VARCHAR(255),
    last_name               VARCHAR(255),
    orders_count            INT,
    state                   VARCHAR(50),
    total_spent             DECIMAL(10, 2),
    last_order_id           BIGINT,
    note                    TEXT,
    verified_email          BOOLEAN,
    multipass_identifier    VARCHAR(255),
    tax_exempt              BOOLEAN,
    tags                    VARCHAR(255),
    last_order_name         VARCHAR(255),
    currency                CHAR(3),
    phone                   VARCHAR(20),
    addresses               jsonb,
    tax_exemptions          jsonb,
    email_marketing_consent jsonb,
    sms_marketing_consent   jsonb,
    admin_graphql_api_id    VARCHAR(255),
    default_address         jsonb
);

--- Create products table
CREATE TABLE shopify.products
(
    id              BIGINT PRIMARY KEY,
    body_html       TEXT,
    created_at      TIMESTAMPTZ,
    handle          VARCHAR(255),
    product_type    VARCHAR(255),
    published_at    TIMESTAMPTZ,
    published_scope VARCHAR(50),
    status          VARCHAR(50),
    tags            VARCHAR(255),
    template_suffix VARCHAR(255),
    title           VARCHAR(255),
    updated_at      TIMESTAMPTZ,
    vendor          VARCHAR(255),
    images          jsonb,
    options         jsonb,
    variants        jsonb
);

--- Indexes for performance improvement
CREATE INDEX idx_orders_contact_email ON shopify.orders (contact_email);
CREATE INDEX idx_customers_email ON shopify.customers (email);
CREATE INDEX idx_products_handle ON shopify.products (handle);