create table if not exists master_catalog_square_variation_mapping
(
    id                         uuid primary key,
    variation_id               varchar(255) not null,
    master_catalog_raw_data_id uuid         not null,
    created_at                 timestamp    not null default now(),
    updated_at                 timestamp    not null default now()
);

create index if not exists master_catalog_square_variation_mapping_master_catalog_raw_data_id_idx on master_catalog_square_variation_mapping (master_catalog_raw_data_id);