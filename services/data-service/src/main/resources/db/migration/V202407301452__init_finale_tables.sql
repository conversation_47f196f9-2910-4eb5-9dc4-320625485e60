-- Create shopify schema
CREATE SCHEMA finale;

--- <PERSON><PERSON> finale
CREATE TABLE finale.available_stock
(
    id  BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    mfc_qoh INTEGER,
    shopify_qoh INTEGER,
    sku varchar(255),
    reservations_qoh INTEGER,
    stock_sublocations VARCHAR(255),
    record_last_updated TIMESTAMP,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

-- Create indexes
CREATE INDEX idx_sku ON finale.available_stock (sku);
CREATE INDEX idx_product_stock_sublocations ON finale.available_stock (stock_sublocations);