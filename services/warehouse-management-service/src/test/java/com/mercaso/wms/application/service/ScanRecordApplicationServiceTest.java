package com.mercaso.wms.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.scanrecord.BatchScanRecordCommand;
import com.mercaso.wms.application.command.scanrecord.BatchScanRecordCommand.ScanRecordCommand;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.domain.scanrecords.ScanRecordRepository;
import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScanRecordApplicationServiceTest {

    private final ScanRecordRepository scanRecordRepository = mock(ScanRecordRepository.class);
    private final DeliveryOrderRepository deliveryOrderRepository = mock(DeliveryOrderRepository.class);
    private final WarehouseRepository warehouseRepository = mock(WarehouseRepository.class);

    private final ScanRecordApplicationService service = new ScanRecordApplicationService(
        scanRecordRepository, deliveryOrderRepository, warehouseRepository);

    private UUID warehouseId;
    private UUID deliveryOrderId;
    private UUID deliveryOrderItemId;
    private UUID userId;
    private String username;
    private Warehouse warehouse;
    private DeliveryOrder deliveryOrder;
    private DeliveryOrderItem deliveryOrderItem;
    private BatchScanRecordCommand command;
    private ScanRecordCommand scanRecordCommand;

    @BeforeEach
    void setUp() {
        warehouseId = UUID.randomUUID();
        deliveryOrderId = UUID.randomUUID();
        deliveryOrderItemId = UUID.randomUUID();
        userId = UUID.randomUUID();
        username = "test-user";

        warehouse = Warehouse.builder()
            .id(warehouseId)
            .name("MDC")
            .build();

        deliveryOrderItem = DeliveryOrderItem.builder()
            .id(deliveryOrderItemId)
            .skuNumber("SKU001")
            .qty(BigDecimal.valueOf(5))
            .build();

        deliveryOrder = DeliveryOrder.builder()
            .id(deliveryOrderId)
            .orderNumber("ORDER001")
            .deliveryOrderItems(Collections.singletonList(deliveryOrderItem))
            .build();

        scanRecordCommand = ScanRecordCommand.builder()
            .orderNumber("ORDER001")
            .skuNumber("SKU001")
            .scanType(ScanType.DELIVERY_SCAN_REMOVED)
            .qty(3)
            .build();

        command = BatchScanRecordCommand.builder()
            .scanRecords(Collections.singletonList(scanRecordCommand))
            .build();
    }

    @Test
    void create_whenValidCommand_thenCreateOrUpdateScanRecordsSuccessfully() {
        // Arrange
        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(Collections.singletonList(deliveryOrder));
        when(scanRecordRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
            mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(userId.toString());
            mockedSecurityContext.when(SecurityContextUtil::getUsername).thenReturn(username);

            // Act
            service.create(command);

            // Assert
            verify(warehouseRepository).findByName("MDC");
            verify(deliveryOrderRepository).findAllByOrderNumberIn(List.of("ORDER001"));
            verify(scanRecordRepository).saveAll(anyList());
        }
    }

    @Test
    void create_whenCommandIsNull_thenReturnEarly() {
        // Act
        service.create(null);

        // Assert
        verify(warehouseRepository, never()).findByName(anyString());
        verify(deliveryOrderRepository, never()).findAllByOrderNumberIn(anyList());
        verify(scanRecordRepository, never()).saveAll(anyList());
    }

    @Test
    void create_whenScanRecordsIsNull_thenReturnEarly() {
        // Arrange
        BatchScanRecordCommand nullCommand = BatchScanRecordCommand.builder()
            .scanRecords(null)
            .build();

        // Act
        service.create(nullCommand);

        // Assert
        verify(warehouseRepository, never()).findByName(anyString());
        verify(deliveryOrderRepository, never()).findAllByOrderNumberIn(anyList());
        verify(scanRecordRepository, never()).saveAll(anyList());
    }

    @Test
    void create_whenScanRecordsIsEmpty_thenReturnEarly() {
        // Arrange
        BatchScanRecordCommand emptyCommand = BatchScanRecordCommand.builder()
            .scanRecords(Collections.emptyList())
            .build();

        // Act
        service.create(emptyCommand);

        // Assert
        verify(warehouseRepository, never()).findByName(anyString());
        verify(deliveryOrderRepository, never()).findAllByOrderNumberIn(anyList());
        verify(scanRecordRepository, never()).saveAll(anyList());
    }

    @Test
    void create_whenWarehouseNotFound_thenThrowException() {
        // Arrange
        when(warehouseRepository.findByName("MDC")).thenReturn(null);

        // Act & Assert
        IllegalStateException exception = assertThrows(IllegalStateException.class,
            () -> service.create(command));
        assertEquals("Warehouse MDC not found", exception.getMessage());

        verify(deliveryOrderRepository, never()).findAllByOrderNumberIn(anyList());
        verify(scanRecordRepository, never()).saveAll(anyList());
    }

    @Test
    void create_whenDeliveryOrderNotFound_thenSkipAndContinue() {
        // Arrange
        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(Collections.emptyList());
        when(scanRecordRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
            mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(userId.toString());
            mockedSecurityContext.when(SecurityContextUtil::getUsername).thenReturn(username);

            // Act
            service.create(command);

            // Assert
            verify(warehouseRepository).findByName("MDC");
            verify(deliveryOrderRepository).findAllByOrderNumberIn(List.of("ORDER001"));
            verify(scanRecordRepository, never()).saveAll(anyList());
        }
    }

    @Test
    void create_whenMultipleScanRecords_thenProcessAllSuccessfully() {
        // Arrange
        ScanRecordCommand scanRecordCommand2 = ScanRecordCommand.builder()
            .orderNumber("ORDER002")
            .skuNumber("SKU002")
            .scanType(ScanType.DELIVERY_SCAN_REMOVED)
            .qty(2)
            .build();

        DeliveryOrderItem deliveryOrderItem2 = DeliveryOrderItem.builder()
            .id(UUID.randomUUID())
            .skuNumber("SKU002")
            .qty(BigDecimal.valueOf(3))
            .build();

        DeliveryOrder deliveryOrder2 = DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER002")
            .deliveryOrderItems(Collections.singletonList(deliveryOrderItem2))
            .build();

        BatchScanRecordCommand multiCommand = BatchScanRecordCommand.builder()
            .scanRecords(Arrays.asList(scanRecordCommand, scanRecordCommand2))
            .build();

        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(deliveryOrderRepository.findAllByOrderNumberIn(Arrays.asList("ORDER001", "ORDER002")))
            .thenReturn(Arrays.asList(deliveryOrder, deliveryOrder2));
        when(scanRecordRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
            mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(userId.toString());
            mockedSecurityContext.when(SecurityContextUtil::getUsername).thenReturn(username);

            // Act
            service.create(multiCommand);

            // Assert
            verify(warehouseRepository).findByName("MDC");
            verify(deliveryOrderRepository).findAllByOrderNumberIn(Arrays.asList("ORDER001", "ORDER002"));
            verify(scanRecordRepository).saveAll(anyList());
        }
    }

    @Test
    void create_whenUserIdIsNull_thenProcessWithNullUserId() {
        // Arrange
        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(Collections.singletonList(deliveryOrder));
        when(scanRecordRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
            mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(null);
            mockedSecurityContext.when(SecurityContextUtil::getUsername).thenReturn(username);

            // Act
            service.create(command);

            // Assert
            verify(warehouseRepository).findByName("MDC");
            verify(deliveryOrderRepository).findAllByOrderNumberIn(List.of("ORDER001"));
            verify(scanRecordRepository).saveAll(anyList());
        }
    }

    @Test
    void create_whenUsernameIsNull_thenProcessWithNullUsername() {
        // Arrange
        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(Collections.singletonList(deliveryOrder));
        when(scanRecordRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
            mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(userId.toString());
            mockedSecurityContext.when(SecurityContextUtil::getUsername).thenReturn(null);

            // Act
            service.create(command);

            // Assert
            verify(warehouseRepository).findByName("MDC");
            verify(deliveryOrderRepository).findAllByOrderNumberIn(List.of("ORDER001"));
            verify(scanRecordRepository).saveAll(anyList());
        }
    }

    @Test
    void create_whenMixedValidAndInvalidRecords_thenProcessValidOnesOnly() {
        // Arrange
        ScanRecordCommand validCommand = ScanRecordCommand.builder()
            .orderNumber("ORDER001")
            .skuNumber("SKU001")
            .scanType(ScanType.DELIVERY_SCAN_REMOVED)
            .qty(3)
            .build();

        ScanRecordCommand invalidOrderCommand = ScanRecordCommand.builder()
            .orderNumber("INVALID_ORDER")
            .skuNumber("SKU001")
            .scanType(ScanType.DELIVERY_SCAN_REMOVED)
            .qty(3)
            .build();

        ScanRecordCommand invalidSkuCommand = ScanRecordCommand.builder()
            .orderNumber("ORDER001")
            .skuNumber("INVALID_SKU")
            .scanType(ScanType.DELIVERY_SCAN_REMOVED)
            .qty(3)
            .build();

        BatchScanRecordCommand mixedCommand = BatchScanRecordCommand.builder()
            .scanRecords(Arrays.asList(validCommand, invalidOrderCommand, invalidSkuCommand))
            .build();

        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(deliveryOrderRepository.findAllByOrderNumberIn(Arrays.asList("ORDER001", "INVALID_ORDER")))
            .thenReturn(Collections.singletonList(deliveryOrder));
        when(scanRecordRepository.saveAll(anyList())).thenReturn(Collections.emptyList());

        try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
            mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(userId.toString());
            mockedSecurityContext.when(SecurityContextUtil::getUsername).thenReturn(username);

            // Act
            service.create(mixedCommand);

            // Assert
            verify(warehouseRepository).findByName("MDC");
            verify(deliveryOrderRepository).findAllByOrderNumberIn(Arrays.asList("ORDER001", "INVALID_ORDER"));
            verify(scanRecordRepository).saveAll(anyList());
        }
    }
}