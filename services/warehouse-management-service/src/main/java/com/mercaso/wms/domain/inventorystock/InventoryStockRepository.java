package com.mercaso.wms.domain.inventorystock;

import com.mercaso.wms.application.query.InventoryStockQuery;
import com.mercaso.wms.domain.BaseDomainRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface InventoryStockRepository extends BaseDomainRepository<InventoryStock, UUID> {

    Page<InventoryStock> search(InventoryStockQuery query, Pageable pageable);

    InventoryStock findBy(UUID warehouseId, String skuNumber, UUID locationId, String lotCode, LocalDate expirationDate);

    InventoryStock findBy(UUID warehouseId, String skuNumber, UUID locationId);

    List<InventoryStock> findByWarehouseIdAndLocationTypes(UUID warehouseId, List<LocationType> locationTypes);

    List<InventoryStock> saveAll(List<InventoryStock> inventoryStocks);

    List<InventoryStock> findAll();

    List<InventoryStock> findByWarehouseIdAndSkuNumbers(UUID warehouseId, List<String> skuNumbers);

    List<StockAggregateProjection> findAvailableStockAggregates(UUID warehouseId, List<LocationType> locationTypes);

}
