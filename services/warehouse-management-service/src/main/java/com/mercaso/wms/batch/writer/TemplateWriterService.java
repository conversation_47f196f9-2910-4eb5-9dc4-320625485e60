package com.mercaso.wms.batch.writer;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.batch.CreateBatchCommand;
import com.mercaso.wms.application.dto.BatchDto;
import com.mercaso.wms.application.service.BatchApplicationService;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.excel.converter.ListToStringConverter;
import com.mercaso.wms.batch.excel.handler.ColourMarkingWriteHandler;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class TemplateWriterService {

    private final DocumentOperations documentOperations;

    private final List<SheetWriter> writers;

    private final BatchJpaDao batchJpaDao;

    private final BatchApplicationService batchApplicationService;

    private static final String FILE_PATH = "1-Mercaso Pick Sheet Template_V7.9.xlsx";

    public BatchDto writeTemplate(WriteTemplateCondition writeTemplateCondition) {
        DocumentResponse documentResponse = generatedDocumentResponse(writeTemplateCondition);
        DocumentResponse originalDocumentResponse = new DocumentResponse();
        originalDocumentResponse.setName(CollectionUtils.isEmpty(writeTemplateCondition.getFileNames()) ? null
            : writeTemplateCondition.getFileNames().getFirst());
        return saveBatch(writeTemplateCondition.getTaggedWith(),
            List.of(originalDocumentResponse),
            documentResponse);
    }

    private BatchDto saveBatch(String taggedWith,
        List<DocumentResponse> originalDocumentResponse,
        DocumentResponse generatedDocument) {
        CreateBatchCommand command = CreateBatchCommand.builder()
            .tag(taggedWith)
            .original(SerializationUtils.toTree(originalDocumentResponse))
            .generated(SerializationUtils.toTree(generatedDocument))
            .lastModifiedBy(SecurityContextUtil.getUsername())
            .build();
        return batchApplicationService.createBatch(command);
    }

    private DocumentResponse generatedDocumentResponse(WriteTemplateCondition writeTemplateCondition) {
        return documentOperations.uploadDocument(UploadDocumentRequest.builder()
            .documentName(generateFileName(getDocRecordsByTag(writeTemplateCondition.getTaggedWith()).size(),
                writeTemplateCondition.getTaggedWith()))
            .content(writeBatchTemplate(writeTemplateCondition))
            .build());
    }

    @NotNull
    private static String generateFileName(int version, String tag) {
        return "Pick Sheet-" + tag
            .concat("-")
            .concat(version + 1 + "") + ".xlsx";
    }

    private List<BatchDo> getDocRecordsByTag(String tag) {
        return batchJpaDao.findByTag(tag);
    }

    public byte[] writeBatchTemplate(WriteTemplateCondition writeTemplateCondition) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Resource resource = new ClassPathResource("template/".concat(FILE_PATH));

        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .registerConverter(new ListToStringConverter())
            .registerWriteHandler(new ColourMarkingWriteHandler())
            .inMemory(false)
            .build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            for (SheetWriter writer : writers) {
                writer.write(excelWriter, fillConfig, writeTemplateCondition);
            }
            excelWriter.finish();
            trackLogs(writeTemplateCondition);
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to generate batch template.", e);
        }
        return outputStream.toByteArray();
    }

    private static void trackLogs(WriteTemplateCondition writeTemplateCondition) {
        try {
            log.info("[MHU] WMS handing unit: {}.",
                writeTemplateCondition.getExcelBatchDtos().stream().mapToInt(ExcelBatchDto::getQuantity).sum());
            writeTemplateCondition.getSourceAndListMap()
                .forEach((key, value) -> log.info("[MHU] {} handing unit: {}.",
                    key,
                    value.stream().mapToInt(ExcelBatchDto::getQuantity).sum()));
        } catch (Exception e) {
            log.error("[MHU] Failed to log the handing unit.", e);
        }
    }

}
