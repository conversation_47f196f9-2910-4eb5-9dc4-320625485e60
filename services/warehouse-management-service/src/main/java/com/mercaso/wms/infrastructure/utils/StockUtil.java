package com.mercaso.wms.infrastructure.utils;

import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StockUtil {

    private StockUtil() {

    }

    public static LocationType getLocationTypeFromSubLocation(String subLocationName, Location location) {
        if (subLocationName == null) {
            return null;
        }
        if (subLocationName.contains("ROOM")) {
            return LocationType.ROOM;
        } else if (subLocationName.contains("RACK")) {
            return LocationType.RACK;
        } else if (subLocationName.contains(".RD") && isNotSpecialUpperBin(subLocationName)) {
            return LocationType.RD;
        } else if (subLocationName.contains(".") && !subLocationName.contains("TRANSFER")
            && !subLocationName.contains("PUTAWAY") && isNotSpecialUpperBin(subLocationName)) {
            return LocationType.SPECIAL_BIN;
        } else {
            return location.getType() == LocationType.BIN ? LocationType.BIN : LocationType.STOCK;
        }
    }

    private static boolean isNotSpecialUpperBin(String locationName) {
        return !locationName.equals(".RD-MFC") && !locationName.contains(".RD-MDC");
    }

    public static Location getLocationType(List<Location> locations, String locationName) {
        return locations.stream()
            .filter(location -> org.apache.commons.lang3.StringUtils.equals(location.getName(), locationName))
            .findFirst()
            .orElse(null);
    }

}
