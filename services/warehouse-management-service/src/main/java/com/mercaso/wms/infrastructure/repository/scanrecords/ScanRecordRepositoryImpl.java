package com.mercaso.wms.infrastructure.repository.scanrecords;

import com.mercaso.wms.domain.scanrecords.ScanRecord;
import com.mercaso.wms.domain.scanrecords.ScanRecordRepository;
import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.scanrecords.jpa.ScanRecordJpaDao;
import com.mercaso.wms.infrastructure.repository.scanrecords.jpa.dataobject.ScanRecordDo;
import com.mercaso.wms.infrastructure.repository.scanrecords.jpa.mapper.ScanRecordDoMapper;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ScanRecordRepositoryImpl implements ScanRecordRepository {

    public final ScanRecordDoMapper mapper;
    public final ScanRecordJpaDao jpaDao;

    @Override
    public List<ScanRecord> saveAll(List<ScanRecord> scanRecords) {
        return mapper.doToDomains(jpaDao.saveAll(mapper.domainToDos(scanRecords)));
    }

    @Override
    public List<ScanRecord> findByScanType(ScanType scanType) {
        return mapper.doToDomains(jpaDao.findByScanType(scanType));
    }

    @Override
    public List<ScanRecord> findByEntityIdsAndScanType(List<UUID> entityIds, ScanType scanType) {
        return mapper.doToDomains(jpaDao.findByEntityIdInAndScanType(entityIds, scanType));
    }

    @Override
    public ScanRecord findByEntityIdAndScanType(UUID entityId, ScanType scanType) {
        return mapper.doToDomain(jpaDao.findByEntityIdAndScanType(entityId, scanType));
    }

    @Override
    public ScanRecord save(ScanRecord domain) {
        ScanRecordDo scanRecordDo = mapper.domainToDo(domain);
        return mapper.doToDomain(jpaDao.save(scanRecordDo));
    }

    @Override
    public ScanRecord findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public ScanRecord update(ScanRecord domain) {
        ScanRecordDo scanRecordDo = jpaDao.findById(domain.getId())
            .orElseThrow(() -> new WmsBusinessException("Outbound scan record not found."));

        ScanRecordDo target = mapper.domainToDo(domain);

        BeanUtils.copyProperties(target, scanRecordDo, "warehouseId", "createdBy", "createdAt");

        return mapper.doToDomain(jpaDao.save(scanRecordDo));
    }
}
