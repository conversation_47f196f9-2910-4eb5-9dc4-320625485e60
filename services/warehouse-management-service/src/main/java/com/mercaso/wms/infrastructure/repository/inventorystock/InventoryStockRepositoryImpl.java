package com.mercaso.wms.infrastructure.repository.inventorystock;

import com.mercaso.wms.application.query.InventoryStockQuery;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.inventorystock.StockAggregateProjection;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.InventoryStockJpaDao;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.dataobject.InventoryStockDo;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.mapper.InventoryStockDoMapper;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class InventoryStockRepositoryImpl implements InventoryStockRepository {

    private final InventoryStockJpaDao jpaDao;
    private final InventoryStockDoMapper mapper;

    @Override
    public InventoryStock save(InventoryStock domain) {
        return mapper.doToDomain(jpaDao.save(mapper.domainToDo(domain)));
    }

    @Override
    public InventoryStock findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public InventoryStock update(InventoryStock domain) {
        InventoryStockDo inventoryStockDo = jpaDao.findById(domain.getId())
            .orElseThrow(() -> new WmsBusinessException("Inventory Stock not found."));

        InventoryStockDo target = mapper.domainToDo(domain);

        BeanUtils.copyProperties(target,
            inventoryStockDo,
            List.of("warehouseId", "createdBy", "createdAt").toArray(new String[0]));

        return mapper.doToDomain(jpaDao.save(inventoryStockDo));
    }

    @Override
    public Page<InventoryStock> search(InventoryStockQuery query, Pageable pageable) {
        return jpaDao.search(query, pageable).map(mapper::doToDomain);
    }

    @Override
    public InventoryStock findBy(UUID warehouseId, String skuNumber, UUID locationId, String lotCode, LocalDate expirationDate) {
        return mapper.doToDomain(jpaDao.findByWarehouseIdAndSkuNumberAndLocationIdAndLotNumberAndExpirationDate(warehouseId,
            skuNumber,
            locationId,
            lotCode,
            expirationDate));
    }

    @Override
    public InventoryStock findBy(UUID warehouseId, String skuNumber, UUID locationId) {
        return mapper.doToDomain(jpaDao.findByWarehouseIdAndSkuNumberAndLocationId(warehouseId, skuNumber, locationId));
    }

    @Override
    public List<InventoryStock> findByWarehouseIdAndLocationTypes(UUID warehouseId, List<LocationType> locationTypes) {
        return mapper.doToDomains(jpaDao.findByWarehouseIdAndLocationTypes(warehouseId, locationTypes));
    }

    @Override
    public List<InventoryStock> saveAll(List<InventoryStock> inventoryStocks) {
        return mapper.doToDomains(jpaDao.saveAll(mapper.domainToDos(inventoryStocks)));
    }

    @Override
    public List<InventoryStock> findAll() {
        return mapper.doToDomains(jpaDao.findAll());
    }

    @Override
    public List<InventoryStock> findByWarehouseIdAndSkuNumbers(UUID warehouseId, List<String> skuNumbers) {
        return mapper.doToDomains(jpaDao.findByWarehouseIdAndSkuNumberIn(warehouseId, skuNumbers));
    }

    @Override
    public List<StockAggregateProjection> findAvailableStockAggregates(UUID warehouseId, List<LocationType> locationTypes) {
        return jpaDao.findAvailableStockAggregates(warehouseId, locationTypes);
    }
}
