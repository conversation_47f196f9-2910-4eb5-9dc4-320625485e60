package com.mercaso.wms.infrastructure.repository.scanrecords.jpa;

import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import com.mercaso.wms.infrastructure.repository.scanrecords.jpa.dataobject.ScanRecordDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ScanRecordJpaDao extends JpaRepository<ScanRecordDo, UUID> {

    List<ScanRecordDo> findByScanType(ScanType scanType);

    List<ScanRecordDo> findByEntityIdInAndScanType(List<UUID> entityIds, ScanType scanType);

    ScanRecordDo findByEntityIdAndScanType(UUID entityId, ScanType scanType);
}