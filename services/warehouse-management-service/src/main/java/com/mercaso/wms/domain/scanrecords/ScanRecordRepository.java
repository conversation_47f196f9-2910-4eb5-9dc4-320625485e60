package com.mercaso.wms.domain.scanrecords;

import com.mercaso.wms.domain.BaseDomainRepository;
import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import java.util.List;
import java.util.UUID;

public interface ScanRecordRepository extends BaseDomainRepository<ScanRecord, UUID> {

    List<ScanRecord> saveAll(List<ScanRecord> scanRecords);

    List<ScanRecord> findByScanType(ScanType scanType);

    List<ScanRecord> findByEntityIdsAndScanType(List<UUID> entityIds, ScanType scanType);

    ScanRecord findByEntityIdAndScanType(UUID entityId, ScanType scanType);

}
