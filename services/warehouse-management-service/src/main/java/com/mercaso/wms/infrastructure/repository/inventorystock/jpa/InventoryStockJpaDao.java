package com.mercaso.wms.infrastructure.repository.inventorystock.jpa;

import com.mercaso.wms.application.query.InventoryStockQuery;
import com.mercaso.wms.domain.inventorystock.StockAggregateProjection;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.dataobject.InventoryStockDo;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface InventoryStockJpaDao extends JpaRepository<InventoryStockDo, UUID> {

    @Query("SELECT i FROM InventoryStockDo i left join i.location l "
        + "where (:#{#query.skuNumbers} is null or i.skuNumber in :#{#query.skuNumbers}) "
        + "and (:#{#query.locationName} is null or l.name = :#{#query.locationName}) "
        + "and (:#{#query.status} is null or i.status = :#{#query.status}) "
        + "and (:#{#query.title} is null or i.title ilike CONCAT('%', :#{#query.title}, '%')) "
        + "and (:#{#query.lotNumber} is null or i.lotNumber = :#{#query.lotNumber}) "
        + "and (:#{#query.lpnNumber} is null or i.lpnNumber = :#{#query.lpnNumber}) ")
    Page<InventoryStockDo> search(@Param("query") InventoryStockQuery query, Pageable pageable);

    InventoryStockDo findByWarehouseIdAndSkuNumberAndLocationIdAndLotNumberAndExpirationDate(UUID warehouseId,
        String skuNumber,
        UUID locationId,
        String lotNumber,
        LocalDate expirationDate);

    @Query(
        "SELECT i FROM InventoryStockDo i where i.warehouse.id = :warehouseId and i.skuNumber = :skuNumber and i.location.id = :locationId")
    InventoryStockDo findByWarehouseIdAndSkuNumberAndLocationId(UUID warehouseId, String skuNumber, UUID locationId);

    @Query("SELECT i FROM InventoryStockDo i where i.location.type in :locationTypes and i.warehouse.id = :warehouseId")
    List<InventoryStockDo> findByWarehouseIdAndLocationTypes(UUID warehouseId, List<LocationType> locationTypes);

    @Query(
        "SELECT i FROM InventoryStockDo i where i.warehouse.id = :warehouseId and i.skuNumber in :skuNumbers and i.status = 'AVAILABLE' and i.qty > 0")
    List<InventoryStockDo> findByWarehouseIdAndSkuNumberIn(UUID warehouseId, List<String> skuNumbers);

    @Query("""
        SELECT 
            s.skuNumber as skuNumber,
            s.title as itemTitle,
            s.location.id as locationId,
            s.location.name as locationName,
            s.location.type as locationType,
            s.qty as quantity,
            s.warehouse.name as warehouseName
        FROM InventoryStockDo s
        WHERE s.warehouse.id = :warehouseId
          AND s.location.type IN :locationTypes
          AND s.status = 'AVAILABLE'
        ORDER BY s.skuNumber, s.location.name
        """)
    List<StockAggregateProjection> findAvailableStockAggregates(
        @Param("warehouseId") UUID warehouseId,
        @Param("locationTypes") List<LocationType> locationTypes
    );

}
