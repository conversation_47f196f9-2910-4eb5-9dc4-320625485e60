package com.mercaso.partnerinsight.service;

import com.mercaso.partnerinsight.dto.TenantStoreSalesInfoDto;
import com.mercaso.partnerinsight.entity.TenantStoreSalesInfo;
import com.mercaso.partnerinsight.mapper.TenantStoreSalesInfoDtoMapper;
import com.mercaso.partnerinsight.repository.TenantStoreSalesInfoRepository;
import com.mercaso.partnerinsight.service.impl.TenantStoreSalesInfoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
class TenantStoreSalesInfoServiceImplTest {

    @Mock
    private TenantStoreSalesInfoRepository tenantStoreSalesInfoRepository;

    @Mock
    private TenantStoreSalesInfoDtoMapper tenantStoreSalesInfoDtoMapper;

    @InjectMocks
    private TenantStoreSalesInfoServiceImpl tenantStoreSalesInfoService;

    private static final String TENANT_ID = "test-tenant-id";
    private static final String STORE_ID = "test-store-id";
    private TenantStoreSalesInfo salesInfo1;
    private TenantStoreSalesInfo salesInfo2;
    private TenantStoreSalesInfoDto salesInfoDto1;
    private TenantStoreSalesInfoDto salesInfoDto2;

    @BeforeEach
    void setUp() {
        Instant currentMonth = Instant.now().truncatedTo(ChronoUnit.DAYS);
        Instant lastMonth = currentMonth.minus(30, ChronoUnit.DAYS);

        // Create test entities
        salesInfo1 = TenantStoreSalesInfo.builder()
            .id(UUID.randomUUID())
            .storeId(STORE_ID)
            .salesQty(200L)
            .salesMonth(currentMonth)
            .tenantId(TENANT_ID)
            .build();

        salesInfo2 = TenantStoreSalesInfo.builder()
            .id(UUID.randomUUID())
            .storeId(STORE_ID)
            .salesQty(150L)
            .salesMonth(lastMonth)
            .tenantId(TENANT_ID)
            .build();

        // Create corresponding DTOs
        salesInfoDto1 = TenantStoreSalesInfoDto.builder()
            .id(salesInfo1.getId())
            .storeId(salesInfo1.getStoreId())
            .salesQty(salesInfo1.getSalesQty())
            .salesMonth(salesInfo1.getSalesMonth())
            .tenantId(salesInfo1.getTenantId())
            .build();

        salesInfoDto2 = TenantStoreSalesInfoDto.builder()
            .id(salesInfo2.getId())
            .storeId(salesInfo2.getStoreId())
            .salesQty(salesInfo2.getSalesQty())
            .salesMonth(salesInfo2.getSalesMonth())
            .tenantId(salesInfo2.getTenantId())
            .build();
    }

    @Test
    void findAllByTenantIdAndStoreId_ShouldReturnListOfTenantStoreSalesInfoDtos() {
        // Given
        List<TenantStoreSalesInfo> salesInfoList = Arrays.asList(salesInfo1, salesInfo2);
        when(tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthAsc(TENANT_ID, STORE_ID)).thenReturn(salesInfoList);
        when(tenantStoreSalesInfoDtoMapper.entityToDto(salesInfo1)).thenReturn(salesInfoDto1);
        when(tenantStoreSalesInfoDtoMapper.entityToDto(salesInfo2)).thenReturn(salesInfoDto2);

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(TENANT_ID, STORE_ID);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(salesInfoDto1.getId(), result.get(0).getId());
        assertEquals(salesInfoDto2.getId(), result.get(1).getId());
        assertEquals(200L, result.get(0).getSalesQty());
        assertEquals(150L, result.get(1).getSalesQty());
    }

    @Test
    void findAllByTenantIdAndStoreId_ShouldReturnEmptyListWhenNoSalesFound() {
        // Given
        when(tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthAsc(TENANT_ID, STORE_ID)).thenReturn(Collections.emptyList());

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(TENANT_ID, STORE_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findAllByTenantIdAndStoreId_ShouldHandleNullResult() {
        // Given
        when(tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthAsc(TENANT_ID, STORE_ID)).thenReturn(List.of());

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(TENANT_ID, STORE_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findAllByTenantIdAndStoreId_ShouldReturnEmptyListWhenTenantIdIsBlank() {
        // Given
        String blankTenantId = "   "; // blank string

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(blankTenantId, STORE_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findAllByTenantIdAndStoreId_ShouldReturnEmptyListWhenTenantIdIsNull() {
        // Given
        String nullTenantId = null;

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(nullTenantId, STORE_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findAllByTenantIdAndStoreId_ShouldReturnEmptyListWhenTenantIdIsEmpty() {
        // Given
        String emptyTenantId = "";

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(emptyTenantId, STORE_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
