package com.mercaso.partnerinsight.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.partnerinsight.AbstractIT;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.entity.TenantItemInfo;
import com.mercaso.partnerinsight.query.repository.CustomizedTenantItemInfoRepository;
import com.mercaso.partnerinsight.utils.TenantItemInfoUtils;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;


class TenantItemInfoRepositoryIT extends AbstractIT {

    @Autowired
    private TenantItemInfoRepository tenantItemInfoRepository;

    @Autowired
    private CustomizedTenantItemInfoRepository customizedTenantItemInfoRepository;

    @Test
    void shouldSuccessWhenSaveAndFindTenantItemInfo() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber = "SKU_" + RandomStringUtils.randomAlphabetic(5);
        String itemName = "Item_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber, itemName);

        // When
        TenantItemInfo savedItem = tenantItemInfoRepository.save(itemInfo);

        // Then
        assertNotNull(savedItem);
        assertNotNull(savedItem.getId());
        assertEquals(skuNumber, savedItem.getSkuNumber());
        assertEquals(itemName, savedItem.getName());
        assertEquals(tenantId, savedItem.getTenantId());
    }

    @Test
    void shouldSuccessWhenSearchBySkuNumber() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber = "SKU_" + RandomStringUtils.randomAlphabetic(5);
        String itemName = "Item_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber, itemName);
        tenantItemInfoRepository.save(itemInfo);

        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .tenantId(tenantId)
            .skuNumber(skuNumber)
            .build();

        // When
        List<TenantItemInfoDto> items = customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query);
        long count = customizedTenantItemInfoRepository.count(query);

        // Then
        assertNotNull(items);
        assertEquals(1, items.size());
        assertEquals(1, count);

        TenantItemInfoDto foundItem = items.getFirst();
        assertEquals(skuNumber, foundItem.getSkuNumber());
        assertEquals(itemName, foundItem.getName());
    }

    @Test
    void shouldReturnEmptyWhenSkuNumberNotFound() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String nonExistentSkuNumber = "NON_EXISTENT_SKU";

        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .tenantId(tenantId)
            .skuNumber(nonExistentSkuNumber)
            .build();

        // When
        List<TenantItemInfoDto> items = customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query);
        long count = customizedTenantItemInfoRepository.count(query);

        // Then
        assertNotNull(items);
        assertTrue(items.isEmpty());
        assertEquals(0, count);
    }

    @Test
    void shouldSuccessWhenSearchWithoutSkuNumber() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber1 = "SKU_1_" + RandomStringUtils.randomAlphabetic(5);
        String skuNumber2 = "SKU_2_" + RandomStringUtils.randomAlphabetic(5);
        String itemName1 = "Item_1_" + RandomStringUtils.randomAlphabetic(5);
        String itemName2 = "Item_2_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo1 = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber1, itemName1);
        TenantItemInfo itemInfo2 = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber2, itemName2);

        tenantItemInfoRepository.save(itemInfo1);
        tenantItemInfoRepository.save(itemInfo2);

        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .tenantId(tenantId)
            .build();

        // When
        List<TenantItemInfoDto> items = customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query);
        long count = customizedTenantItemInfoRepository.count(query);

        // Then
        assertNotNull(items);
        assertEquals(2, count);
        assertEquals(2, items.size());
        assertTrue(items.stream().anyMatch(item -> item.getSkuNumber().equals(skuNumber1)));
        assertTrue(items.stream().anyMatch(item -> item.getSkuNumber().equals(skuNumber2)));
    }

    @Test
    void shouldSuccessWhenSearchWithSortBySalesQty() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber1 = "SKU_1_" + RandomStringUtils.randomAlphabetic(5);
        String skuNumber2 = "SKU_2_" + RandomStringUtils.randomAlphabetic(5);
        String itemName1 = "Item_1_" + RandomStringUtils.randomAlphabetic(5);
        String itemName2 = "Item_2_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo1 = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber1, itemName1);
        itemInfo1.setSalesQty(100L);
        itemInfo1.setAggregationLevel("30");

        TenantItemInfo itemInfo2 = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber2, itemName2);
        itemInfo2.setSalesQty(200L);
        itemInfo2.setAggregationLevel("30");

        tenantItemInfoRepository.save(itemInfo1);
        tenantItemInfoRepository.save(itemInfo2);

        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .tenantId(tenantId)
            .sort(TenantItemInfoQuery.SortType.SALES_QTY_DESC)
            .build();

        // When
        List<TenantItemInfoDto> items = customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query);

        // Then
        assertNotNull(items);
        assertEquals(2, items.size());
        assertEquals(skuNumber2, items.get(0).getSkuNumber()); // Higher sales qty should be first
        assertEquals(skuNumber1, items.get(1).getSkuNumber());
    }
}