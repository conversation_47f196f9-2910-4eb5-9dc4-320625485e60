package com.mercaso.partnerinsight.utils;

import com.mercaso.partnerinsight.entity.TenantItemInfo;
import com.mercaso.partnerinsight.enums.AggregationLevelEnums;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TenantItemInfoUtils {

    public static TenantItemInfo buildTenantItemInfo(String tenantId, String skuNumber, String name) {
        return TenantItemInfo.builder()
            .id(UUID.randomUUID())
            .skuNumber(skuNumber)
            .upc("UPC_" + RandomStringUtils.randomAlphabetic(8))
            .name(name)
            .photo("photo_url")
            .packageSize(10)
            .salesQty(100L)
            .aggregationLevel(AggregationLevelEnums.LAST_MONTH.name())
            .tenantId(tenantId)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .build();
    }
}