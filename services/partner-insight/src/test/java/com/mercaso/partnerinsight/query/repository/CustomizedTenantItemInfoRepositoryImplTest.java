package com.mercaso.partnerinsight.query.repository;

import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.enums.AggregationLevelEnums;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class CustomizedTenantItemInfoRepositoryImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private CustomizedTenantItemInfoRepositoryImpl repository;

    private static final String TENANT_ID = "test_tenant_1";
    private static final String SKU_NUMBER = "SKU001";

    @BeforeEach
    void setUp() {
        // Set up mock behavior here if needed
    }

    @Test
    void shouldReturnPagedResults() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .page(1)
            .pageSize(10)
            .build();

        // Use lenient stubbing
        lenient().when(repository.tenantItemInfoPageQuery(query)).thenReturn(Collections.emptyList());

        // When
        List<TenantItemInfoDto> results = repository.tenantItemInfoPageQuery(query);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void shouldFilterBySkuNumber() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .skuNumber(SKU_NUMBER)
            .build();

        // Use lenient stubbing
        lenient().when(repository.tenantItemInfoPageQuery(query)).thenReturn(Collections.emptyList());

        // When
        List<TenantItemInfoDto> results = repository.tenantItemInfoPageQuery(query);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void shouldFilterByTenantId() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .tenantId(TENANT_ID)
            .build();

        // Use lenient stubbing
        lenient().when(repository.tenantItemInfoPageQuery(query)).thenReturn(Collections.emptyList());

        // When
        List<TenantItemInfoDto> results = repository.tenantItemInfoPageQuery(query);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void shouldSortBySalesQty30Days() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .aggregationLevel(AggregationLevelEnums.LAST_MONTH.name())
            .sort(TenantItemInfoQuery.SortType.SALES_QTY_DESC)
            .build();

        // Use lenient stubbing
        lenient().when(repository.tenantItemInfoPageQuery(query)).thenReturn(Collections.emptyList());

        // When
        List<TenantItemInfoDto> results = repository.tenantItemInfoPageQuery(query);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void shouldSortBySalesQty90Days() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .aggregationLevel(AggregationLevelEnums.LAST_QUARTER.name())
            .sort(TenantItemInfoQuery.SortType.SALES_QTY_DESC)
            .build();

        // Use lenient stubbing
        lenient().when(repository.tenantItemInfoPageQuery(query)).thenReturn(Collections.emptyList());

        // When
        List<TenantItemInfoDto> results = repository.tenantItemInfoPageQuery(query);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void shouldSortBySalesQtyTotalDays() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .aggregationLevel(AggregationLevelEnums.TOTAL.name())
            .sort(TenantItemInfoQuery.SortType.SALES_QTY_DESC)
            .build();

        // Use lenient stubbing
        lenient().when(repository.tenantItemInfoPageQuery(query)).thenReturn(Collections.emptyList());

        // When
        List<TenantItemInfoDto> results = repository.tenantItemInfoPageQuery(query);

        // Then
        assertThat(results).isEmpty();
    }

    @Test
    void shouldReturnCorrectCountWithFilters() {
        // Given
        TenantItemInfoQuery query = TenantItemInfoQuery.builder()
            .tenantId(TENANT_ID)
            .skuNumber(SKU_NUMBER)
            .build();

        // Mock the behavior of the repository method to return a list with one element
        lenient().when(jdbcTemplate.query(
            anyString(),
            any(PreparedStatementSetter.class),
            any(RowMapper.class)
        )).thenReturn(Lists.newArrayList(1L));

        // When
        long count = repository.count(query);

        // Then
        assertThat(count).isEqualTo(1L);
    }

    @Test
    void shouldReturnTenantAggregationLevelItemSalesInfo() {
        // Given
        String tenantId = "test_tenant_1";
        
        List<TenantItemSalesInfoDto> expectedResult = List.of(
            createTenantItemSalesInfoDto("LAST_MONTH", 10L, 100L),
            createTenantItemSalesInfoDto("LAST_QUARTER", 15L, 200L),
            createTenantItemSalesInfoDto("TOTAL", 20L, 300L)
        );
        
        lenient().when(jdbcTemplate.query(
            anyString(),
            any(PreparedStatementSetter.class),
            any(RowMapper.class)
        )).thenReturn(expectedResult);
        
        // When
        List<TenantItemSalesInfoDto> results = repository.tenantAggregationLevelItemSalesInfo(tenantId);
        
        // Then
        assertThat(results).isNotNull().hasSize(3).containsExactlyElementsOf(expectedResult);
    }

    @Test
    void shouldReturnEmptyListWhenNoAggregationLevelData() {
        // Given
        String tenantId = "test_tenant_1";
        
        lenient().when(jdbcTemplate.query(
            anyString(),
            any(PreparedStatementSetter.class),
            any(RowMapper.class)
        )).thenReturn(Collections.emptyList());
        
        // When
        List<TenantItemSalesInfoDto> results = repository.tenantAggregationLevelItemSalesInfo(tenantId);
        
        // Then
        assertThat(results).isNotNull().isEmpty();
    }

    // Helper method for creating test data
    private TenantItemSalesInfoDto createTenantItemSalesInfoDto(String aggregationLevel, Long itemCount, Long salesQty) {
        TenantItemSalesInfoDto dto = new TenantItemSalesInfoDto();
        dto.setAggregationLevel(aggregationLevel);
        dto.setItemCount(itemCount);
        dto.setSalesQty(salesQty);
        return dto;
    }
}