package com.mercaso.partnerinsight.query.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.partnerinsight.dto.PageInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.query.repository.CustomizedTenantItemInfoRepository;
import com.mercaso.partnerinsight.query.service.impl.TenantItemInfoSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class TenantItemInfoSearchServiceImplTest {

    @Mock
    private CustomizedTenantItemInfoRepository customizedTenantItemInfoRepository;

    @InjectMocks
    private TenantItemInfoSearchServiceImpl tenantItemInfoSearchService;

    private TenantItemInfoQuery query;
    private TenantItemInfoDto tenantItemInfoDto;

    @BeforeEach
    void setUp() {
        query = TenantItemInfoQuery.builder()
            .tenantId("test-tenant")
            .skuNumber("test-sku")
            .build();

        tenantItemInfoDto = TenantItemInfoDto.builder()
            .skuNumber("test-sku")
            .name("Test Item")
            .build();
    }

    @Test
    void searchTenantItemInfoList_ShouldReturnPageInfoWithData() {
        // Given
        List<TenantItemInfoDto> expectedData = Collections.singletonList(tenantItemInfoDto);
        when(customizedTenantItemInfoRepository.count(query)).thenReturn(1L);
        when(customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query)).thenReturn(expectedData);

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoSearchService.searchTenantItemInfoList(query);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getTotalCount());
        assertEquals(expectedData, result.getData());
        verify(customizedTenantItemInfoRepository).count(query);
        verify(customizedTenantItemInfoRepository).tenantItemInfoPageQuery(query);
    }

    @Test
    void searchTenantItemInfoList_WhenNoDataFound_ShouldReturnEmptyPageInfo() {
        // Given
        List<TenantItemInfoDto> emptyList = Collections.emptyList();
        when(customizedTenantItemInfoRepository.count(query)).thenReturn(0L);
        when(customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query)).thenReturn(emptyList);

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoSearchService.searchTenantItemInfoList(query);

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());
        assertEquals(emptyList, result.getData());
        verify(customizedTenantItemInfoRepository).count(query);
        verify(customizedTenantItemInfoRepository).tenantItemInfoPageQuery(query);
    }

    @Test
    void searchTenantItemInfoList_WithMultipleItems_ShouldReturnCorrectPageInfo() {
        // Given
        TenantItemInfoDto secondItem = TenantItemInfoDto.builder()
            .skuNumber("test-sku-2")
            .name("Test Item 2")
            .build();

        List<TenantItemInfoDto> expectedData = Arrays.asList(tenantItemInfoDto, secondItem);
        when(customizedTenantItemInfoRepository.count(query)).thenReturn(2L);
        when(customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query)).thenReturn(expectedData);

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoSearchService.searchTenantItemInfoList(query);

        // Then
        assertNotNull(result);
        assertEquals(2L, result.getTotalCount());
        assertEquals(expectedData, result.getData());
        assertEquals(2, result.getData().size());
        verify(customizedTenantItemInfoRepository).count(query);
        verify(customizedTenantItemInfoRepository).tenantItemInfoPageQuery(query);
    }

    @Test
    void searchTenantItemInfoList_ShouldReturnEmptyListWhenTenantIdIsBlank() {

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoSearchService.searchTenantItemInfoList(query);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void shouldReturnEmptyResultWhenTenantIdIsBlank() {
        // Arrange
        TenantItemInfoQuery blankTenantIdQuery = TenantItemInfoQuery.builder().tenantId("").build();

        // Act
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoSearchService.searchTenantItemInfoList(blankTenantIdQuery);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotalCount());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void searchTenantAggregationLevelItemSalesInfo_ShouldReturnItemSalesInfoList() {
        // Given
        String tenantId = "test-tenant";
        TenantItemSalesInfoDto salesInfoDto = TenantItemSalesInfoDto.builder()
            .aggregationLevel("SKU")
            .itemCount(10L)
            .salesQty(100L)
            .build();
        List<TenantItemSalesInfoDto> expectedResult = Collections.singletonList(salesInfoDto);

        when(customizedTenantItemInfoRepository.tenantAggregationLevelItemSalesInfo(tenantId))
            .thenReturn(expectedResult);

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoSearchService.searchTenantAggregationLevelItemSalesInfo(tenantId);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(customizedTenantItemInfoRepository).tenantAggregationLevelItemSalesInfo(tenantId);
    }

    @Test
    void searchTenantAggregationLevelItemSalesInfo_WithMultipleItems_ShouldReturnAllItems() {
        // Given
        String tenantId = "test-tenant";
        TenantItemSalesInfoDto salesInfoDto1 = TenantItemSalesInfoDto.builder()
            .aggregationLevel("SKU")
            .itemCount(10L)
            .salesQty(100L)
            .build();
        TenantItemSalesInfoDto salesInfoDto2 = TenantItemSalesInfoDto.builder()
            .aggregationLevel("CATEGORY")
            .itemCount(5L)
            .salesQty(50L)
            .build();
        List<TenantItemSalesInfoDto> expectedResult = Arrays.asList(salesInfoDto1, salesInfoDto2);

        when(customizedTenantItemInfoRepository.tenantAggregationLevelItemSalesInfo(tenantId))
            .thenReturn(expectedResult);

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoSearchService.searchTenantAggregationLevelItemSalesInfo(tenantId);

        // Then
        assertNotNull(result);
        assertEquals(expectedResult, result);
        assertEquals(2, result.size());
        verify(customizedTenantItemInfoRepository).tenantAggregationLevelItemSalesInfo(tenantId);
    }

    @Test
    void searchTenantAggregationLevelItemSalesInfo_WhenTenantIdIsBlank_ShouldReturnEmptyList() {
        // Given
        String blankTenantId = "";

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoSearchService.searchTenantAggregationLevelItemSalesInfo(blankTenantId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void searchTenantAggregationLevelItemSalesInfo_WhenTenantIdIsNull_ShouldReturnEmptyList() {
        // Given
        String nullTenantId = null;

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoSearchService.searchTenantAggregationLevelItemSalesInfo(nullTenantId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void searchTenantAggregationLevelItemSalesInfo_WhenRepositoryReturnsEmptyList_ShouldReturnEmptyList() {
        // Given
        String tenantId = "test-tenant";
        when(customizedTenantItemInfoRepository.tenantAggregationLevelItemSalesInfo(tenantId))
            .thenReturn(Collections.emptyList());

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoSearchService.searchTenantAggregationLevelItemSalesInfo(tenantId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(customizedTenantItemInfoRepository).tenantAggregationLevelItemSalesInfo(tenantId);
    }
}
