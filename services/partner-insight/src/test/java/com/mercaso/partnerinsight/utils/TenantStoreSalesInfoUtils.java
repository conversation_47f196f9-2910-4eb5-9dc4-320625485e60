package com.mercaso.partnerinsight.utils;

import com.mercaso.partnerinsight.entity.TenantStoreSalesInfo;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TenantStoreSalesInfoUtils {

    public static TenantStoreSalesInfo buildTenantStoreSalesInfo(String tenantId, String storeId) {
        return TenantStoreSalesInfo.builder()
            .id(UUID.randomUUID())
            .storeId(storeId)
            .salesQty(150L)
            .salesMonth(Instant.now().truncatedTo(ChronoUnit.DAYS))
            .tenantId(tenantId)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .build();
    }

    public static TenantStoreSalesInfo buildTenantStoreSalesInfo(String tenantId, String storeId, Long salesQty, Instant salesMonth) {
        return TenantStoreSalesInfo.builder()
            .id(UUID.randomUUID())
            .storeId(storeId)
            .salesQty(salesQty)
            .salesMonth(salesMonth)
            .tenantId(tenantId)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .build();
    }
}
