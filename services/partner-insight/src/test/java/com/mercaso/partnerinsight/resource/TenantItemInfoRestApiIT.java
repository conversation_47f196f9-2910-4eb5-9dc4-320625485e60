package com.mercaso.partnerinsight.resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.mercaso.partnerinsight.AbstractIT;
import com.mercaso.partnerinsight.config.SecurityContextUtilWrapper;
import com.mercaso.partnerinsight.dto.PageInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.entity.TenantItemInfo;
import com.mercaso.partnerinsight.enums.AggregationLevelEnums;
import com.mercaso.partnerinsight.repository.TenantItemInfoRepository;
import com.mercaso.partnerinsight.utils.TenantItemInfoUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

@Execution(ExecutionMode.SAME_THREAD)
class TenantItemInfoRestApiIT extends AbstractIT {

    @Autowired
    private TenantItemInfoRepository tenantItemInfoRepository;

    @MockBean
    protected SecurityContextUtilWrapper securityContextUtilWrapper;

    @Test
    void shouldSuccessWhenSearchTenantItemInfoList() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber = "SKU_" + RandomStringUtils.randomAlphabetic(5);
        String itemName = "Item_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber, itemName);
        tenantItemInfoRepository.save(itemInfo);

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoRestApiUtil.searchTenantItemInfoList(skuNumber,
            AggregationLevelEnums.LAST_MONTH.name());

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1, result.getTotalCount());
        assertEquals(skuNumber, result.getData().getFirst().getSkuNumber());
        assertEquals(itemName, result.getData().getFirst().getName());
    }

    @Test
    void shouldReturnEmptyWhenSkuNumberNotFound() throws Exception {
        // Given
        String nonExistentSkuNumber = "NON_EXISTENT_SKU";

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoRestApiUtil.searchTenantItemInfoList(nonExistentSkuNumber, "");

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(0, result.getTotalCount());
        assertTrue(result.getData().isEmpty());
    }


    @Test
    void shouldSuccessWhenSearchTenantItemInfoListAggregationLevelEmpty() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber = "SKU_" + RandomStringUtils.randomAlphabetic(5);
        String itemName = "Item_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber, itemName);
        tenantItemInfoRepository.save(itemInfo);

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        PageInfoDto<TenantItemInfoDto> result = tenantItemInfoRestApiUtil.searchTenantItemInfoList(skuNumber, "");

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(0, result.getTotalCount());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void shouldSuccessWhenSearchTenantAggregationLevelItemSalesInfo() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String skuNumber = "SKU_" + RandomStringUtils.randomAlphabetic(5);
        String itemName = "Item_" + RandomStringUtils.randomAlphabetic(5);

        TenantItemInfo itemInfo = TenantItemInfoUtils.buildTenantItemInfo(tenantId, skuNumber, itemName);
        tenantItemInfoRepository.save(itemInfo);

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoRestApiUtil.searchTenantAggregationLevelItemSalesInfo();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.getFirst().getItemCount());
        assertEquals(100L, result.getFirst().getSalesQty());
        assertEquals(AggregationLevelEnums.LAST_MONTH.name(), result.getFirst().getAggregationLevel());
    }

    @Test
    void shouldReturnEmptyListWhenNoSalesInfoFound() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        List<TenantItemSalesInfoDto> result = tenantItemInfoRestApiUtil.searchTenantAggregationLevelItemSalesInfo();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}