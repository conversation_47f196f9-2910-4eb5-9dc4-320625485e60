package com.mercaso.partnerinsight.query.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.query.repository.CustomizedTenantItemInfoRepositoryImpl.TenantItemInfoDynamicSearch;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {TenantItemInfoDynamicSearch.class})
@ExtendWith(SpringExtension.class)
class DynamicSearchConditionTest {

    @Autowired
    private DynamicSearchCondition<TenantItemInfoQuery> dynamicSearchCondition;


    @Test
    void testGenerateConditionBlock_given42_thenReturnAndTenantIdAndSkuNumber() {
        // Arrange
        TenantItemInfoQuery tenantItemInfoQuery = mock(TenantItemInfoQuery.class);
        when(tenantItemInfoQuery.getSkuNumber()).thenReturn("42");
        when(tenantItemInfoQuery.getTenantId()).thenReturn("42");

        // Act
        String actualGenerateConditionBlockResult = dynamicSearchCondition.generateConditionBlock(tenantItemInfoQuery);

        // Assert
        verify(tenantItemInfoQuery).getSkuNumber();
        verify(tenantItemInfoQuery).getTenantId();
        assertEquals(" AND tenant_id = ?  AND sku_number = ? ", actualGenerateConditionBlockResult);
    }

    @Test
    void testBindSqlParameterWithPsQueryIndex_given42_thenReturnThree() throws SQLException {
        // Arrange
        PreparedStatement ps = mock(PreparedStatement.class);
        doNothing().when(ps).setString(anyInt(), Mockito.<String>any());
        TenantItemInfoQuery tenantItemInfoQuery = mock(TenantItemInfoQuery.class);
        when(tenantItemInfoQuery.getSkuNumber()).thenReturn("42");
        when(tenantItemInfoQuery.getTenantId()).thenReturn("42");

        // Act
        int actualBindSqlParameterResult = dynamicSearchCondition.bindSqlParameter(ps, tenantItemInfoQuery, 1);

        // Assert
        verify(tenantItemInfoQuery, atLeast(1)).getSkuNumber();
        verify(tenantItemInfoQuery, atLeast(1)).getTenantId();
        verify(ps, atLeast(1)).setString(anyInt(), eq("42"));
        assertEquals(3, actualBindSqlParameterResult);
    }

    @Test
    void testBindSqlParameterWithPsQuery_given42_thenReturnThree() throws SQLException {
        // Arrange
        PreparedStatement ps = mock(PreparedStatement.class);
        doNothing().when(ps).setString(anyInt(), Mockito.<String>any());
        TenantItemInfoQuery tenantItemInfoQuery = mock(TenantItemInfoQuery.class);
        when(tenantItemInfoQuery.getSkuNumber()).thenReturn("42");
        when(tenantItemInfoQuery.getTenantId()).thenReturn("42");

        // Act
        int actualBindSqlParameterResult = dynamicSearchCondition.bindSqlParameter(ps, tenantItemInfoQuery);

        // Assert
        verify(tenantItemInfoQuery, atLeast(1)).getSkuNumber();
        verify(tenantItemInfoQuery, atLeast(1)).getTenantId();
        verify(ps, atLeast(1)).setString(anyInt(), eq("42"));
        assertEquals(3, actualBindSqlParameterResult);
    }
}
