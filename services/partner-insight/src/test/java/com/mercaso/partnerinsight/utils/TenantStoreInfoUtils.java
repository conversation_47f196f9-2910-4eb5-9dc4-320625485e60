package com.mercaso.partnerinsight.utils;

import com.mercaso.partnerinsight.entity.TenantStoreInfo;
import com.mercaso.partnerinsight.enums.StoreSourceEnums;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TenantStoreInfoUtils {

    public static TenantStoreInfo buildTenantStoreInfo(String tenantId, String name) {
        return TenantStoreInfo.builder()
            .id(UUID.randomUUID())
            .storeId(UUID.randomUUID().toString())
            .name(name)
            .latitude(34.16424)
            .longitude(-118.52207)
            .salesQty(100L)
            .address("Test Address")
            .source(StoreSourceEnums.MERCASO)
            .tenantId(tenantId)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .build();
    }
}
