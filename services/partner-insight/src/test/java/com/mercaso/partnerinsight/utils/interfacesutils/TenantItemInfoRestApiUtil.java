package com.mercaso.partnerinsight.utils.interfacesutils;

import com.mercaso.partnerinsight.dto.PageInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.Map;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class TenantItemInfoRestApiUtil extends IntegrationTestRestUtil {
    private static final String GET_TENANT_ITEM_INFO_PAGE_URL = "/v1/tenant-item-info";
    private static final String SEARCH_TENANT_AGGREGATION_LEVEL_ITEM_SALES_INFO_URL = "/v1/tenant-item-info/aggregation-level/sales-info";


    public TenantItemInfoRestApiUtil(Environment environment) {
        super(environment);
    }

    public PageInfoDto<TenantItemInfoDto> searchTenantItemInfoList(String skuNumber, String aggregationLevel) throws Exception {
        Map<String, String> params = Map.of("skuNumber", skuNumber, "aggregationLevel", aggregationLevel);
        return searchEntityList(GET_TENANT_ITEM_INFO_PAGE_URL, params, TenantItemInfoDto.class);
    }

    public List<TenantItemSalesInfoDto> searchTenantAggregationLevelItemSalesInfo() throws Exception {
        return getEntityList(SEARCH_TENANT_AGGREGATION_LEVEL_ITEM_SALES_INFO_URL, TenantItemSalesInfoDto.class);
    }


}