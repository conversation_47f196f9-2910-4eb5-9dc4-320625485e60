package com.mercaso.partnerinsight.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mercaso.partnerinsight.dto.PageInfoDto;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.springframework.boot.test.web.client.LocalHostUriTemplateHandler;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


public abstract class IntegrationTestRestUtil {

    private final LocalHostUriTemplateHandler uriTemplateHandler;

    private final RestTemplate restTemplate = new RestTemplate();

    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    private final String userProfileJson = "";

    public IntegrationTestRestUtil(Environment environment) {
        this.uriTemplateHandler = new LocalHostUriTemplateHandler(environment);

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        restTemplate.setRequestFactory(requestFactory);
    }

    private HttpHeaders createHeaders() {
        return createHeaders(MediaType.APPLICATION_JSON);
    }

    private HttpHeaders createHeaders(MediaType type) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        if (userProfileJson != null) {
            headers.set("UserProfile", userProfileJson);
        }
        return headers;
    }

    public <T> PageInfoDto<T> searchEntityList(String url, Map<String, String> params, Class<T> dtoClass)
        throws Exception {
        HttpEntity<String> entity = new HttpEntity<>(null, createHeaders());
        ResponseEntity<String> response = performRequest(url, params, entity, HttpMethod.GET);

        Assert.assertEquals(HttpStatus.OK, response.getStatusCode());
        JavaType type = objectMapper.getTypeFactory().constructParametricType(PageInfoDto.class, dtoClass);
        return objectMapper.readValue(response.getBody(), type);
    }

    protected <T> List<T> getEntityList(String url, Class<T> dtoClass) throws Exception {
        return getEntityList(url, null, dtoClass);
    }

    public <T> List<T> getEntityList(String url, Map<String, String> params, Class<T> dtoClass)
        throws Exception {
        HttpEntity<String> entity = new HttpEntity<>(null, createHeaders());
        ResponseEntity<String> response = performRequest(url, params, entity, HttpMethod.GET);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, dtoClass);
        return objectMapper.readValue(response.getBody(), listType);
    }

    public ResponseEntity<String> performRequest(String path,
        Map<String, String> params,
        HttpEntity<String> entity,
        HttpMethod method) {
        String url = uriTemplateHandler.getRootUri() + path;

        if (params != null) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            builder.build(false);
            params.forEach(builder::queryParam);
            url = builder.toUriString();
        }

        if (entity == null) {
            entity = new HttpEntity<>(null, createHeaders());
        }

        return restTemplate.exchange(url, method, entity, String.class);
    }


}
