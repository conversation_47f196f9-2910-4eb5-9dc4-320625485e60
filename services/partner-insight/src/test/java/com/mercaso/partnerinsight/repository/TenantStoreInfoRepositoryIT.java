package com.mercaso.partnerinsight.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.partnerinsight.AbstractIT;
import com.mercaso.partnerinsight.entity.TenantStoreInfo;
import com.mercaso.partnerinsight.utils.TenantStoreInfoUtils;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class TenantStoreInfoRepositoryIT extends AbstractIT {

    @Autowired
    private TenantStoreInfoRepository tenantStoreInfoRepository;

    @Test
    void shouldSuccessWhenFindAllByTenantId() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String storeName1 = "Store_1_" + RandomStringUtils.randomAlphabetic(5);
        String storeName2 = "Store_2_" + RandomStringUtils.randomAlphabetic(5);

        TenantStoreInfo storeInfo1 = TenantStoreInfoUtils.buildTenantStoreInfo(tenantId, storeName1);
        TenantStoreInfo storeInfo2 = TenantStoreInfoUtils.buildTenantStoreInfo(tenantId, storeName2);

        tenantStoreInfoRepository.save(storeInfo1);
        tenantStoreInfoRepository.save(storeInfo2);

        // When
        List<TenantStoreInfo> result = tenantStoreInfoRepository.findAllByTenantIdOrderBySalesQtyDescItemCountDesc(tenantId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(store -> store.getName().equals(storeName1)));
        assertTrue(result.stream().anyMatch(store -> store.getName().equals(storeName2)));
    }

    @Test
    void shouldReturnEmptyListWhenTenantIdNotFound() {
        // Given
        String nonExistentTenantId = "NON_EXISTENT_TENANT_ID";

        // When
        List<TenantStoreInfo> result = tenantStoreInfoRepository.findAllByTenantIdOrderBySalesQtyDescItemCountDesc(nonExistentTenantId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}