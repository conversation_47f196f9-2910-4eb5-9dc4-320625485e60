package com.mercaso.partnerinsight.service;

import com.mercaso.partnerinsight.dto.TenantStoreInfoDto;
import com.mercaso.partnerinsight.entity.TenantStoreInfo;
import com.mercaso.partnerinsight.enums.StoreSourceEnums;
import com.mercaso.partnerinsight.mapper.TenantStoreInfoDtoMapper;
import com.mercaso.partnerinsight.repository.TenantStoreInfoRepository;
import com.mercaso.partnerinsight.service.impl.TenantStoreInfoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
class TenantStoreInfoServiceImplTest {

    @Mock
    private TenantStoreInfoRepository tenantStoreInfoRepository;

    @Mock
    private TenantStoreInfoDtoMapper tenantStoreInfoDtoMapper;

    @InjectMocks
    private TenantStoreInfoServiceImpl tenantStoreInfoService;

    private static final String TENANT_ID = "test-tenant-id";
    private TenantStoreInfo storeInfo1;
    private TenantStoreInfo storeInfo2;
    private TenantStoreInfoDto storeInfoDto1;
    private TenantStoreInfoDto storeInfoDto2;

    @BeforeEach
    void setUp() {
        // Create test entities
        storeInfo1 = TenantStoreInfo.builder()
            .id(UUID.randomUUID())
            .storeId("store-1")
            .name("Store One")
            .latitude(34.16424)
            .longitude(-118.52207)
            .salesQty(100L)
            .address("Test Address 1")
            .source(StoreSourceEnums.MERCASO)
            .tenantId(TENANT_ID)
            .build();

        storeInfo2 = TenantStoreInfo.builder()
            .id(UUID.randomUUID())
            .storeId("store-2")
            .name("Store Two")
            .latitude(35.16424)
            .longitude(-119.52207)
            .salesQty(200L)
            .address("Test Address 2")
            .source(StoreSourceEnums.MERCASO)
            .tenantId(TENANT_ID)
            .build();

        // Create corresponding DTOs
        storeInfoDto1 = TenantStoreInfoDto.builder()
            .id(storeInfo1.getId())
            .name(storeInfo1.getName())
            .latitude(storeInfo1.getLatitude())
            .longitude(storeInfo1.getLongitude())
            .salesQty(storeInfo1.getSalesQty())
            .address(storeInfo1.getAddress())
            .source(storeInfo1.getSource())
            .build();

        storeInfoDto2 = TenantStoreInfoDto.builder()
            .id(storeInfo2.getId())
            .name(storeInfo2.getName())
            .latitude(storeInfo2.getLatitude())
            .longitude(storeInfo2.getLongitude())
            .salesQty(storeInfo2.getSalesQty())
            .address(storeInfo2.getAddress())
            .source(storeInfo2.getSource())
            .build();
    }

    @Test
    void findAllByTenantId_ShouldReturnListOfTenantStoreInfoDtos() {
        // Given
        List<TenantStoreInfo> storeInfoList = Arrays.asList(storeInfo1, storeInfo2);
        when(tenantStoreInfoRepository.findAllByTenantIdOrderBySalesQtyDescItemCountDesc(TENANT_ID)).thenReturn(storeInfoList);
        when(tenantStoreInfoDtoMapper.entityToDto(storeInfo1)).thenReturn(storeInfoDto1);
        when(tenantStoreInfoDtoMapper.entityToDto(storeInfo2)).thenReturn(storeInfoDto2);

        // When
        List<TenantStoreInfoDto> result = tenantStoreInfoService.findAllByTenantId(TENANT_ID);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(storeInfoDto1.getId(), result.get(0).getId());
        assertEquals(storeInfoDto2.getId(), result.get(1).getId());
    }

    @Test
    void findAllByTenantId_ShouldReturnEmptyListWhenNoStoresFound() {
        // Given
        when(tenantStoreInfoRepository.findAllByTenantIdOrderBySalesQtyDescItemCountDesc(TENANT_ID)).thenReturn(Collections.emptyList());

        // When
        List<TenantStoreInfoDto> result = tenantStoreInfoService.findAllByTenantId(TENANT_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findAllByTenantId_ShouldHandleNullResult() {
        // Given
        when(tenantStoreInfoRepository.findAllByTenantIdOrderBySalesQtyDescItemCountDesc(TENANT_ID)).thenReturn(List.of());

        // When
        List<TenantStoreInfoDto> result = tenantStoreInfoService.findAllByTenantId(TENANT_ID);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findAllByTenantId_ShouldReturnEmptyListWhenTenantIdIsBlank() {
        // Given
        String blankTenantId = "   "; // or you can use "" for an empty string

        // When
        List<TenantStoreInfoDto> result = tenantStoreInfoService.findAllByTenantId(blankTenantId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}