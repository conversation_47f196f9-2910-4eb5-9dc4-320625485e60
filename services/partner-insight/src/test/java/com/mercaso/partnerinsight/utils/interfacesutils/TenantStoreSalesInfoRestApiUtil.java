package com.mercaso.partnerinsight.utils.interfacesutils;

import com.mercaso.partnerinsight.dto.TenantStoreSalesInfoDto;
import com.mercaso.partnerinsight.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class TenantStoreSalesInfoRestApiUtil extends IntegrationTestRestUtil {
    private static final String GET_TENANT_STORE_SALES_INFO_LIST = "/v1/tenant-store-sales-info";

    public TenantStoreSalesInfoRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<TenantStoreSalesInfoDto> findStoreSalesInfoByTenantIdAndStoreId(String storeId) throws Exception {
        String url = GET_TENANT_STORE_SALES_INFO_LIST + "/" + storeId;
        return getEntityList(url, TenantStoreSalesInfoDto.class);
    }
}
