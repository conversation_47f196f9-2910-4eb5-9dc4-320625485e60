package com.mercaso.partnerinsight;

import com.mercaso.partnerinsight.utils.interfacesutils.TenantItemInfoRestApiUtil;
import com.mercaso.partnerinsight.utils.interfacesutils.TenantStoreInfoRestApiUtil;
import com.mercaso.partnerinsight.utils.interfacesutils.TenantStoreSalesInfoRestApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;


@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
        TenantStoreInfoRestApiUtil.class,
        TenantItemInfoRestApiUtil.class,
        TenantStoreSalesInfoRestApiUtil.class,
})
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EmbeddedKafka(count = 3)
@AutoConfigureMockMvc
public abstract class AbstractIT {

    @Autowired
    protected TenantStoreInfoRestApiUtil tenantStoreInfoRestApiUtil;

    @Autowired
    protected TenantItemInfoRestApiUtil tenantItemInfoRestApiUtil;

    @Autowired
    protected TenantStoreSalesInfoRestApiUtil tenantStoreSalesInfoRestApiUtil;


}
