package com.mercaso.partnerinsight.resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.mercaso.partnerinsight.AbstractIT;
import com.mercaso.partnerinsight.config.SecurityContextUtilWrapper;
import com.mercaso.partnerinsight.dto.TenantStoreInfoDto;
import com.mercaso.partnerinsight.entity.TenantStoreInfo;
import com.mercaso.partnerinsight.repository.TenantStoreInfoRepository;
import com.mercaso.partnerinsight.utils.TenantStoreInfoUtils;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

@Execution(ExecutionMode.SAME_THREAD)
class TenantStoreInfoRestApiIT extends AbstractIT {

    @Autowired
    private TenantStoreInfoRepository tenantStoreInfoRepository;

    @MockBean
    protected SecurityContextUtilWrapper securityContextUtilWrapper;

    @Test
    void shouldSuccessWhenFindAllByTenantId() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String storeName1 = "Store_1_" + RandomStringUtils.randomAlphabetic(5);
        String storeName2 = "Store_2_" + RandomStringUtils.randomAlphabetic(5);

        TenantStoreInfo storeInfo1 = TenantStoreInfoUtils.buildTenantStoreInfo(tenantId, storeName1);
        TenantStoreInfo storeInfo2 = TenantStoreInfoUtils.buildTenantStoreInfo(tenantId, storeName2);

        tenantStoreInfoRepository.save(storeInfo1);
        tenantStoreInfoRepository.save(storeInfo2);

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        List<TenantStoreInfoDto> result = tenantStoreInfoRestApiUtil.findAllByTenantId();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

    }


}